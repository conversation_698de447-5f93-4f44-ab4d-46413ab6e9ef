<template>
  <div class="admin-card">
    <!-- 操作按钮区域 -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium">剧本和计划书管理</h3>
      <div class="space-x-2">
        <a-button type="primary" @click="handleAdd">
          <template #icon>
            <Plus class="size-4" />
          </template>
          添加文档
        </a-button>
        <a-button type="primary" :loading="saving" @click="handleSave">
          保存文档信息
        </a-button>
      </div>
    </div>

    <!-- 文档列表 -->
    <template v-if="localDocuments && localDocuments.length > 0">
      <div class="space-y-4">
        <div
          v-for="(document, index) in localDocuments"
          :key="document.id || `temp-${index}`"
          class="border border-gray-200 rounded-lg p-4"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center space-x-4 mb-2">
                <span class="font-medium">{{ document.name || '未命名文档' }}</span>
                <span class="text-sm text-gray-500">{{ document.fileType || '' }}</span>
                <span class="text-sm text-gray-500">{{ document.fileSize || '' }}</span>
              </div>
              <div class="text-sm text-gray-600">
                {{ document.fileUrl || '暂无文件链接' }}
              </div>
              <div class="text-xs text-gray-400 mt-1">
                创建时间: {{ formatDate(document.createdAt || '') }}
              </div>
            </div>
            <div class="flex space-x-2 ml-4">
              <a-button size="small" @click="handleEdit(document, index)">
                编辑
              </a-button>
              <a-button size="small" danger @click="handleDelete(index)">
                删除
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="text-center py-8 text-gray-500">
        暂无剧本文档信息
        <div class="mt-2">
          <a-button type="link" @click="handleAdd">点击添加第一个文档</a-button>
        </div>
      </div>
    </template>

    <!-- 编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      :width="600"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="modalFormRef"
        :model="modalFormState"
        :rules="modalRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        layout="horizontal"
      >
        <a-form-item label="文档名称" name="name">
          <a-input
            v-model:value="modalFormState.name"
            placeholder="请输入文档名称"
          />
        </a-form-item>

        <!-- 文件上传区域 -->
        <a-form-item label="文件上传">
          <a-upload-dragger
            :show-upload-list="false"
            :before-upload="handleBeforeUpload"
            :custom-request="handleCustomUpload"
            accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.rtf"
            :disabled="uploading"
          >
            <p class="ant-upload-drag-icon">
              <IconifyIcon icon="ant-design:inbox-outlined" class="text-3xl text-gray-400" />
            </p>
            <p class="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p class="ant-upload-hint">
              支持 PDF、DOC、DOCX、PPT、PPTX、TXT、RTF 格式，最大50MB
            </p>
          </a-upload-dragger>

          <!-- 上传进度 -->
          <div v-if="uploading" class="mt-2">
            <a-progress :percent="uploadProgress" status="active" />
            <p class="text-sm text-gray-500 mt-1">正在上传文件...</p>
          </div>
        </a-form-item>

        <a-form-item label="文件链接" name="fileUrl">
          <a-input
            v-model:value="modalFormState.fileUrl"
            placeholder="文件上传后自动填充"
            :disabled="true"
          />
        </a-form-item>

        <a-form-item label="文件类型" name="fileType">
          <a-input
            v-model:value="modalFormState.fileType"
            placeholder="文件上传后自动识别"
            :disabled="true"
          />
        </a-form-item>

        <a-form-item label="文件大小" name="fileSize">
          <a-input
            v-model:value="modalFormState.fileSize"
            placeholder="文件上传后自动计算"
            :disabled="true"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import {
  Button as AButton,
  Modal as AModal,
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  Upload as AUpload,
  UploadDragger as AUploadDragger,
  Progress as AProgress,
  message
} from 'ant-design-vue';
import { Plus, IconifyIcon } from '@vben/icons';
import {
  createDramaDocument,
  updateDramaDocument,
  deleteDramaDocument,
  uploadDramaDocument
} from '#/api/drama-management';

interface Document {
  id?: number;
  dramaId?: number;
  name: string;
  fileUrl: string;
  fileType?: string;
  fileSize?: string;
  createdAt?: string;
}

interface Props {
  dramaId: number;
  documents: Document[];
}

const props = defineProps<Props>();

// 定义emits
const emit = defineEmits<{
  saveSuccess: [message?: string];
}>();

// 状态
const saving = ref(false);
const modalVisible = ref(false);
const modalTitle = ref('');
const editingIndex = ref(-1);
const modalFormRef = ref();
const uploading = ref(false);
const uploadProgress = ref(0);

// 本地文档数据
const localDocuments = ref<Document[]>([]);

// 弹窗表单状态
const modalFormState = reactive({
  name: '',
  fileUrl: '',
  fileType: '',
  fileSize: '',
});

// 弹窗表单验证规则
const modalRules = {
  name: [
    { required: true, message: '请输入文档名称', trigger: 'blur' },
    { min: 2, max: 100, message: '文档名称长度应在2-100个字符之间', trigger: 'blur' }
  ],
  fileUrl: [
    { required: true, message: '请输入文件链接', trigger: 'blur' }
  ]
};

// 初始化本地数据
const initLocalData = () => {
  localDocuments.value = props.documents ? [...props.documents] : [];
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知';
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

// 文件上传前验证
const handleBeforeUpload = (file: File) => {
  // 验证文件类型
  const allowedTypes = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.txt', '.rtf'];
  const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();

  if (!allowedTypes.includes(fileExt)) {
    message.error(`不支持的文件类型，请上传 ${allowedTypes.join(', ')} 格式的文件`);
    return false;
  }

  // 验证文件大小 (最大50MB)
  const maxSize = 50 * 1024 * 1024; // 50MB
  if (file.size > maxSize) {
    message.error('文件大小不能超过50MB');
    return false;
  }

  return true;
};

// 自定义文件上传
const handleCustomUpload = async (options: any) => {
  const { file } = options;

  try {
    uploading.value = true;
    uploadProgress.value = 0;

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10;
      }
    }, 200);

    // 调用上传接口
    const result = await uploadDramaDocument(props.dramaId, file);

    // 清除进度定时器
    clearInterval(progressInterval);
    uploadProgress.value = 100;

    // 自动填充表单数据
    Object.assign(modalFormState, {
      name: modalFormState.name || result.name,
      fileUrl: result.fileUrl,
      fileType: result.fileType,
      fileSize: result.fileSize,
    });

    message.success('文件上传成功');
  } catch (error: any) {
    console.error('文件上传失败:', error);
    message.error(error.message || '文件上传失败，请重试');
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
  }
};

// 添加文档
const handleAdd = () => {
  modalTitle.value = '添加剧本文档';
  editingIndex.value = -1;

  // 重置表单
  Object.assign(modalFormState, {
    name: '',
    fileUrl: '',
    fileType: '',
    fileSize: '',
  });

  modalVisible.value = true;
};

// 编辑文档
const handleEdit = (document: Document, index: number) => {
  modalTitle.value = '编辑剧本文档';
  editingIndex.value = index;

  // 填充表单数据
  Object.assign(modalFormState, {
    name: document.name || '',
    fileUrl: document.fileUrl || '',
    fileType: document.fileType || '',
    fileSize: document.fileSize || '',
  });

  modalVisible.value = true;
};

// 删除文档
const handleDelete = async (index: number) => {
  const document = localDocuments.value[index];

  // 如果是新添加的文档（没有id），直接从本地删除
  if (!document.id) {
    localDocuments.value.splice(index, 1);
    message.success('删除成功');
    return;
  }

  try {
    await deleteDramaDocument(props.dramaId, document.id);
    localDocuments.value.splice(index, 1);
    message.success('删除成功');
  } catch (error: any) {
    console.error('删除剧本文档失败:', error);
    message.error(error.message || '删除失败，请重试');
  }
};

// 弹窗确认
const handleModalOk = async () => {
  try {
    await modalFormRef.value.validate();

    const documentData = {
      name: modalFormState.name,
      fileUrl: modalFormState.fileUrl,
      fileType: modalFormState.fileType,
      fileSize: modalFormState.fileSize,
    };

    if (editingIndex.value >= 0) {
      // 编辑模式
      const existingDocument = localDocuments.value[editingIndex.value];

      if (existingDocument.id) {
        // 更新已存在的文档
        await updateDramaDocument(props.dramaId, existingDocument.id, documentData);
        localDocuments.value[editingIndex.value] = {
          ...existingDocument,
          ...documentData,
        };
      } else {
        // 更新本地新增的文档
        localDocuments.value[editingIndex.value] = {
          ...existingDocument,
          ...documentData,
          dramaId: props.dramaId,
        };
      }
      message.success('修改成功');
    } else {
      // 新增模式
      const result = await createDramaDocument(props.dramaId, documentData);
      localDocuments.value.push({
        id: result.id,
        dramaId: props.dramaId,
        name: documentData.name,
        fileUrl: documentData.fileUrl,
        fileType: documentData.fileType,
        fileSize: documentData.fileSize,
        createdAt: result.createdAt,
      });
      message.success('添加成功');
    }

    modalVisible.value = false;
  } catch (error: any) {
    console.error('操作失败:', error);
    message.error(error.message || '操作失败，请重试');
  }
};

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 保存文档信息
const handleSave = async () => {
  try {
    saving.value = true;

    // 检查是否有未保存的本地新增数据
    const unsavedDocuments = localDocuments.value.filter(d => !d.id);

    if (unsavedDocuments.length > 0) {
      // 批量保存未保存的文档
      for (const document of unsavedDocuments) {
        const result = await createDramaDocument(props.dramaId, {
          name: document.name,
          fileUrl: document.fileUrl,
          fileType: document.fileType,
          fileSize: document.fileSize,
        });

        // 更新本地数据，添加服务器返回的ID
        const index = localDocuments.value.findIndex(d => d === document);
        if (index >= 0) {
          localDocuments.value[index] = {
            ...document,
            id: result.id,
            createdAt: result.createdAt,
          };
        }
      }
    }

    emit('saveSuccess', '剧本文档保存成功');
  } catch (error: any) {
    console.error('保存文档信息失败:', error);
    message.error(error.message || '保存失败，请重试');
  } finally {
    saving.value = false;
  }
};

// 监听props变化，重新初始化本地数据
watch(() => props.documents, () => {
  initLocalData();
}, { immediate: true, deep: true });
</script>

<style scoped>
.admin-card {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-x-2 > * + * {
  margin-left: 0.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

:deep(.ant-form-item-label) {
  @apply font-medium;
}
</style>
