# 剧投投管理后台

基于 Vue Vben Admin 5.0 的现代化管理后台系统，专为短剧投资平台设计。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **TypeScript** - JavaScript 的超集
- **Ant Design Vue** - 企业级 UI 组件库
- **Pinia** - Vue 状态管理
- **Vue Router** - Vue 路由管理

## 项目结构

```
fundAdmin/
├── apps/                   # 应用目录
│   ├── web-antd/          # Ant Design 版本
│   ├── web-ele/           # Element Plus 版本
│   ├── web-naive/         # Naive UI 版本
│   └── backend-mock/      # Mock 后端服务
├── packages/              # 共享包
│   ├── @core/            # 核心包
│   ├── effects/          # 副作用包
│   ├── icons/            # 图标包
│   ├── locales/          # 国际化
│   ├── preferences/      # 偏好设置
│   ├── stores/           # 状态管理
│   ├── styles/           # 样式
│   ├── types/            # 类型定义
│   └── utils/            # 工具函数
├── internal/             # 内部工具
├── scripts/              # 脚本文件
└── docs/                 # 文档
```

## 功能特性

- ✅ 用户管理
- ✅ 角色权限管理
- ✅ 菜单管理
- ✅ 基金产品管理
- ✅ 短剧项目管理
- ✅ 演员管理
- ✅ 轮播图管理
- ✅ 标签管理
- ✅ 系统设置
- ✅ 审计日志
- ✅ 文件上传
- ✅ 数据统计

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
# 启动 Ant Design 版本
pnpm dev:antd

# 启动 Element Plus 版本
pnpm dev:ele

# 启动 Naive UI 版本
pnpm dev:naive
```

### 构建生产版本

```bash
# 构建 Ant Design 版本
pnpm build:antd

# 构建 Element Plus 版本
pnpm build:ele

# 构建 Naive UI 版本
pnpm build:naive
```

## 开发指南

### 目录说明

- `apps/web-antd/` - 基于 Ant Design Vue 的管理后台
- `packages/@core/` - 核心功能包
- `packages/effects/` - 副作用处理
- `packages/stores/` - 状态管理
- `packages/types/` - TypeScript 类型定义

### 开发规范

- 使用 TypeScript 进行开发
- 遵循 ESLint 和 Prettier 规范
- 组件使用 Composition API
- 状态管理使用 Pinia

### 权限管理

系统采用基于角色的权限控制（RBAC）：
- 超级管理员：拥有所有权限
- 普通管理员：根据角色分配权限
- 权限控制到菜单和按钮级别

### API 集成

- 使用统一的请求客户端
- 支持请求/响应拦截器
- 自动处理错误和加载状态
- 支持请求取消和重试

## 部署

### 环境配置

```bash
# 开发环境
VITE_API_URL=http://localhost:3001/api

# 生产环境
VITE_API_URL=https://api.mengtu.tv/api
```

### 构建部署

```bash
# 构建
pnpm build:antd

# 部署到服务器
# 将 dist 目录上传到 Web 服务器
```

## 许可证

本项目基于 Vue Vben Admin 开发，遵循 MIT 许可证。
