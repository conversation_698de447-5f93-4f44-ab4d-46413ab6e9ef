/**
 * 创建短剧文档接口
 * POST /api/admin/dramas/[id]/documents
 */
export default defineEventHandler(async (event) => {
  try {
    // 验证管理员权限
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 获取短剧ID
    const dramaId = getRouterParam(event, 'id');
    if (!dramaId || isNaN(Number(dramaId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的短剧ID'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { name, fileUrl, fileType, fileSize } = body;

    // 验证必填字段
    if (!name || name.trim() === '') {
      throw createError({
        statusCode: 400,
        statusMessage: '文档名称不能为空'
      });
    }

    if (!fileUrl || fileUrl.trim() === '') {
      throw createError({
        statusCode: 400,
        statusMessage: '文件链接不能为空'
      });
    }

    // 检查短剧是否存在
    const dramaExists = await query(
      'SELECT id FROM drama_series WHERE id = ?',
      [dramaId]
    );

    if (dramaExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '短剧不存在'
      });
    }

    // 插入短剧文档
    const result = await query(
      `INSERT INTO drama_documents (drama_id, name, file_url, file_type, file_size, created_at) 
       VALUES (?, ?, ?, ?, ?, NOW())`,
      [dramaId, name.trim(), fileUrl.trim(), fileType || null, fileSize || null]
    );

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_CREATE_DRAMA_DOCUMENT',
      description: `管理员创建短剧文档: 短剧ID=${dramaId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      metadata: {
        dramaId: Number(dramaId),
        name: name.trim(),
        fileUrl: fileUrl.trim(),
        fileType: fileType || null,
        fileSize: fileSize || null,
        documentId: result.insertId
      }
    });

    logger.info('短剧文档创建成功', {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      dramaId: Number(dramaId),
      documentId: result.insertId,
      name: name.trim()
    });

    return {
      success: true,
      message: '短剧文档创建成功',
      data: {
        id: result.insertId,
        dramaId: Number(dramaId),
        name: name.trim(),
        fileUrl: fileUrl.trim(),
        fileType: fileType || null,
        fileSize: fileSize || null,
        createdAt: new Date().toISOString()
      }
    };

  } catch (error: any) {
    logger.error('创建短剧文档失败', {
      error: error.message,
      dramaId: getRouterParam(event, 'id')
    });

    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || '创建短剧文档失败'
    });
  }
});
