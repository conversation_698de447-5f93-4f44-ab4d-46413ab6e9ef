<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';

import { ref, onMounted } from 'vue';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';
import { $t } from '@vben/locales';

import { Button, message, Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  deleteNews,
  getNewsList,
  publishNews,
  batchOperateNews,
  getAllCategories,
  type NewsManagementApi,
} from '#/api/news-management';

import { useColumns, useGridFormSchema, NEWS_STATUS_CONFIG } from './data';
import Form from './modules/form.vue';

defineOptions({
  name: 'NewsManagementList',
});

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
  onSuccess: () => {
    gridApi.reload();
  },
});

// 分类选项
const categoryOptions = ref<Array<{ label: string; value: number }>>([]);

// 加载分类选项
async function loadCategories() {
  try {
    const res = await getAllCategories();
    categoryOptions.value = res.data.map(cat => ({
      label: cat.name,
      value: cat.id,
    }));
  } catch (error) {
    console.error('加载分类失败:', error);
  }
}

function confirm(content: string, title?: string) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: title || '确认',
      content,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        resolve(true);
      },
      onCancel: () => {
        reject(new Error('用户取消操作'));
      },
    });
  });
}

// 操作处理
function onActionClick(e: OnActionClickParams<NewsManagementApi.News>) {
  switch (e.action) {
    case 'edit':
      onEdit(e.row);
      break;
    case 'delete':
      onDelete(e.row);
      break;
    case 'publish':
      onPublish(e.row);
      break;
    case 'unpublish':
      onUnpublish(e.row);
      break;
    case 'preview':
      onPreview(e.row);
      break;
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: useGridFormSchema(),
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    showOverflow: true,
    columnConfig: {
      resizable: true,
    },
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const params = { ...formValues };
          
          // 处理日期范围
          if (params.dateRange && Array.isArray(params.dateRange)) {
            params.startDate = params.dateRange[0];
            params.endDate = params.dateRange[1];
            delete params.dateRange;
          }

          const res = await getNewsList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...params,
          });

          return {
            result: res.list || [],
            total: res.pagination?.total || 0,
          };
        },
      },
      response: {
        result: 'result',
        total: 'total',
      },
    },
    pagerConfig: {},
    rowConfig: {
      keyField: 'id',
    },
    checkboxConfig: {
      reserve: true,
    },
    toolbarConfig: {
      refresh: true,
      zoom: true,
      custom: true,
    },
  } as VxeTableGridOptions,
});

// 新增新闻
async function onCreate() {
  const isSuccess = await formDrawerApi.open({
    title: '新增新闻',
    data: {
      status: 'draft',
      is_featured: false,
    },
  });

  if (isSuccess) {
    gridApi.reload();
    message.success('新闻创建成功');
  }
}

// 编辑新闻
async function onEdit(row: NewsManagementApi.News) {
  const isSuccess = await formDrawerApi.open({
    title: '编辑新闻',
    data: {
      ...row,
      category_id: row.category?.id,
      tags: row.tags?.map(tag => tag.name) || [],
    },
  });

  if (isSuccess) {
    gridApi.reload();
    message.success('新闻更新成功');
  }
}

// 删除新闻
async function onDelete(row: NewsManagementApi.News) {
  try {
    await confirm(
      `确定要删除新闻"${row.title}"吗？删除后不可恢复。`,
      '删除确认'
    );

    await deleteNews(row.id);
    gridApi.reload();
    message.success('新闻删除成功');
  } catch (error: any) {
    if (error.message !== '用户取消操作') {
      message.error(error.message || '删除失败');
    }
  }
}

// 发布新闻
async function onPublish(row: NewsManagementApi.News) {
  try {
    await confirm(
      `确定要发布新闻"${row.title}"吗？`,
      '发布确认'
    );

    await publishNews(row.id, { action: 'publish' });
    gridApi.reload();
    message.success('新闻发布成功');
  } catch (error: any) {
    if (error.message !== '用户取消操作') {
      message.error(error.message || '发布失败');
    }
  }
}

// 下线新闻
async function onUnpublish(row: NewsManagementApi.News) {
  try {
    await confirm(
      `确定要下线新闻"${row.title}"吗？`,
      '下线确认'
    );

    await publishNews(row.id, { action: 'unpublish' });
    gridApi.reload();
    message.success('新闻已下线');
  } catch (error: any) {
    if (error.message !== '用户取消操作') {
      message.error(error.message || '下线失败');
    }
  }
}

// 预览新闻
function onPreview(row: NewsManagementApi.News) {
  // 打开新窗口预览新闻
  const previewUrl = `/news/${row.id}`;
  window.open(previewUrl, '_blank');
}

// 批量操作
async function onBatchOperation(action: string) {
  const selectedRows = gridApi.getCheckboxRecords();
  
  if (selectedRows.length === 0) {
    message.warning('请先选择要操作的新闻');
    return;
  }

  const actionMap = {
    publish: '发布',
    unpublish: '下线',
    delete: '删除',
    archive: '归档',
  };

  try {
    await confirm(
      `确定要${actionMap[action]}选中的 ${selectedRows.length} 条新闻吗？`,
      '批量操作确认'
    );

    const ids = selectedRows.map(row => row.id);
    await batchOperateNews({ ids, action });
    
    gridApi.reload();
    message.success(`批量${actionMap[action]}成功`);
  } catch (error: any) {
    if (error.message !== '用户取消操作') {
      message.error(error.message || '操作失败');
    }
  }
}

onMounted(() => {
  loadCategories();
});
</script>

<template>
  <Page
    auto-content-height
    description="管理网站新闻内容，包括创建、编辑、发布和删除新闻文章"
    title="新闻管理"
  >
    <template #extra>
      <Space>
        <Button 
          type="primary" 
          @click="onCreate"
        >
          <template #icon>
            <Plus class="size-4" />
          </template>
          新增新闻
        </Button>
        
        <Button 
          @click="onBatchOperation('publish')"
        >
          批量发布
        </Button>
        
        <Button 
          @click="onBatchOperation('unpublish')"
        >
          批量下线
        </Button>
        
        <Button 
          danger
          @click="onBatchOperation('delete')"
        >
          批量删除
        </Button>
      </Space>
    </template>

    <Grid />
    
    <FormDrawer />
  </Page>
</template>
