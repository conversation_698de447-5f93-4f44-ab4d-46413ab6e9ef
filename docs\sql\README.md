# 剧投投募资管理系统 - SQL脚本说明

## 概述

本目录包含剧投投募资管理系统的所有数据库脚本文件，用于数据库初始化、结构更新、数据修复和系统维护。

## 脚本分类

### 1. 初始化脚本

#### `init-db.sql` - 数据库初始化脚本
**用途**：创建完整的数据库结构和基础数据
**执行时机**：项目首次部署时
**包含内容**：
- 数据库创建
- 所有表结构定义
- 基础数据插入（管理员、角色、权限、菜单）
- 索引和外键约束

**执行顺序**：1（最先执行）

```bash
mysql -u root -p < docs/sql/init-db.sql
```

### 2. 结构更新脚本

#### `create-audit-logs.sql` - 创建审计日志表
**用途**：创建审计日志表，用于记录系统操作日志
**执行时机**：需要启用审计功能时
**依赖**：需要先执行 `init-db.sql`

**执行顺序**：2

```bash
mysql -u root -p < docs/sql/create-audit-logs.sql
```

#### `update-users-table.sql` - 更新用户表结构
**用途**：更新用户表结构，添加用户类型等字段
**执行时机**：用户表结构需要升级时
**注意事项**：会删除 `is_admin` 字段，请确保数据备份

**执行顺序**：3

```bash
mysql -u root -p < docs/sql/update-users-table.sql
```

#### `add-banner-menu.sql` - 添加Banner管理菜单
**用途**：在管理后台添加Banner管理相关菜单
**执行时机**：需要启用Banner管理功能时
**依赖**：需要先执行 `init-db.sql`

**执行顺序**：4

```bash
mysql -u root -p < docs/sql/add-banner-menu.sql
```

### 3. 数据修复脚本

#### `fix_fund_usage_plans_schema.sql` - 修复基金使用计划表结构
**用途**：优化基金资金使用计划表的字段结构
**执行时机**：基金管理功能升级时
**注意事项**：会备份原数据，请确认迁移结果

**执行顺序**：5

```bash
mysql -u root -p < docs/sql/fix_fund_usage_plans_schema.sql
```

#### `fix_existing_usage_plans_data.sql` - 修复现有使用计划数据
**用途**：修复和清理基金使用计划的数据
**执行时机**：在 `fix_fund_usage_plans_schema.sql` 之后执行
**依赖**：需要先执行结构修复脚本

**执行顺序**：6

```bash
mysql -u root -p < docs/sql/fix_existing_usage_plans_data.sql
```

#### `optimize_fund_timelines.sql` - 优化基金时间线表
**用途**：优化基金时间线表的日期存储格式
**执行时机**：基金时间线功能升级时
**特点**：支持开放式日期，提供向后兼容视图

**执行顺序**：7

```bash
mysql -u root -p < docs/sql/optimize_fund_timelines.sql
```

### 4. 验证脚本

#### `verify_users_table.sql` - 验证用户表数据
**用途**：验证用户表结构和数据的完整性
**执行时机**：用户系统部署后或数据迁移后
**功能**：
- 检查表结构
- 验证数据完整性
- 创建测试用户
- 生成数据统计报告

**执行顺序**：8（最后执行）

```bash
mysql -u root -p < docs/sql/verify_users_table.sql
```

## 完整部署流程

### 新项目部署

```bash
# 1. 初始化数据库
mysql -u root -p < docs/sql/init-db.sql

# 2. 创建审计日志表
mysql -u root -p < docs/sql/create-audit-logs.sql

# 3. 添加Banner管理菜单
mysql -u root -p < docs/sql/add-banner-menu.sql

# 4. 验证部署结果
mysql -u root -p < docs/sql/verify_users_table.sql
```

### 现有项目升级

```bash
# 1. 备份数据库
mysqldump -u root -p mengtu > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 更新用户表结构
mysql -u root -p < docs/sql/update-users-table.sql

# 3. 修复基金相关表结构
mysql -u root -p < docs/sql/fix_fund_usage_plans_schema.sql
mysql -u root -p < docs/sql/fix_existing_usage_plans_data.sql
mysql -u root -p < docs/sql/optimize_fund_timelines.sql

# 4. 验证升级结果
mysql -u root -p < docs/sql/verify_users_table.sql
```

## 注意事项

### 执行前准备
1. **备份数据库**：执行任何脚本前都要备份数据库
2. **检查依赖**：确保按照正确的顺序执行脚本
3. **测试环境**：先在测试环境验证脚本

### 安全提醒
1. **生产环境**：生产环境执行前请仔细审查脚本
2. **权限控制**：确保数据库用户有足够的权限
3. **数据验证**：执行后验证数据完整性

### 故障恢复
1. **回滚计划**：准备回滚方案
2. **监控日志**：关注执行过程中的错误信息
3. **数据校验**：执行后进行数据校验

## 测试用户

执行 `verify_users_table.sql` 后会创建以下测试用户：

| 用户名 | 登录方式 | 密码 | 用户类型 |
|--------|----------|------|----------|
| testuser_email | <EMAIL> | test123456 | 投资者 |
| testuser_phone | 13800138888 | test123456 | 投资者 |

## 联系信息

如有问题，请联系开发团队或查看项目文档。
