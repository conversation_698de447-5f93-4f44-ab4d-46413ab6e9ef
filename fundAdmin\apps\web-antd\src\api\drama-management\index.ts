import { requestClient } from '#/api/request';
import type { DramaManagementApi } from './types';

/**
 * 获取短剧列表
 */
export async function getDramaList(params: DramaManagementApi.DramaListParams = {}) {
  return requestClient.get<DramaManagementApi.DramaListResponse>('/admin/dramas', {
    params,
  });
}

/**
 * 获取短剧详情
 */
export async function getDramaDetail(id: number) {
  return requestClient.get<DramaManagementApi.DramaDetailResponse>(`/admin/dramas/${id}`);
}

/**
 * 创建短剧
 */
export async function createDrama(data: DramaManagementApi.CreateDramaParams) {
  return requestClient.post<DramaManagementApi.OperationResponse>('/admin/dramas', data);
}

/**
 * 更新短剧
 */
export async function updateDrama(id: number, data: DramaManagementApi.UpdateDramaParams) {
  return requestClient.put<DramaManagementApi.OperationResponse>(`/admin/dramas/${id}`, data);
}

/**
 * 更新短剧制作排期
 */
export async function updateDramaSchedule(id: number, data: { schedule: any }) {
  return requestClient.put<DramaManagementApi.OperationResponse>(`/admin/dramas/${id}/schedule`, data);
}

/**
 * 更新短剧募资信息
 */
export async function updateDramaFunding(id: number, data: { funding: any }) {
  return requestClient.put<DramaManagementApi.OperationResponse>(`/admin/dramas/${id}/funding`, data);
}

/**
 * 更新短剧投资权益
 */
export async function updateDramaInvestmentTiers(id: number, data: { investmentTiers: any[] }) {
  return requestClient.put<DramaManagementApi.OperationResponse>(`/admin/dramas/${id}/investment-tiers`, data);
}

/**
 * 更新短剧其他信息
 */
export async function updateDramaAdditionalInfo(id: number, data: { additionalInfo: any }) {
  return requestClient.put<DramaManagementApi.OperationResponse>(`/admin/dramas/${id}/additional-info`, data);
}

/**
 * 删除短剧
 */
export async function deleteDrama(id: number) {
  return requestClient.delete<DramaManagementApi.OperationResponse>(`/admin/dramas/${id}`);
}

/**
 * 获取短剧素材列表
 */
export async function getMaterialList(dramaId: number) {
  return requestClient.get<{ list: DramaManagementApi.DramaMaterial[] }>(`/admin/dramas/${dramaId}/materials`);
}

/**
 * 创建短剧素材
 */
export async function createMaterial(data: DramaManagementApi.CreateMaterialParams) {
  const { dramaId, ...materialData } = data;
  return requestClient.post<DramaManagementApi.OperationResponse>(`/admin/dramas/${dramaId}/materials`, materialData);
}

/**
 * 删除短剧素材
 */
export async function deleteMaterial(dramaId: number, materialId: number) {
  return requestClient.delete<DramaManagementApi.OperationResponse>(`/admin/dramas/${dramaId}/materials/${materialId}`);
}

// 获取标签列表（用于选择器）
export function getTagsForSelector() {
  return requestClient.get<any>('/admin/content-management/tags/selector');
}

// 获取平台列表（用于选择器）
export function getPlatformsForSelector() {
  return requestClient.get<any>('/admin/content-management/platforms/selector');
}

// 获取厂牌列表（用于选择器）
export function getBrandsForSelector() {
  return requestClient.get<any>('/admin/brand-management/selector');
}

// 获取演员列表（用于选择器）
export function getActorsForSelector() {
  return requestClient.get<any>('/admin/actors/selector');
}

/**
 * 上传短剧封面
 * @param file 图片文件
 * @returns 上传结果
 */
export async function uploadDramaCover(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post('/admin/upload/drama-cover', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 短剧剧本文档相关接口
 */
export async function createDramaDocument(dramaId: number, data: { name: string; fileUrl: string; fileType?: string; fileSize?: string }) {
  return requestClient.post<DramaManagementApi.OperationResponse>(`/admin/dramas/${dramaId}/documents`, data);
}

export async function updateDramaDocument(dramaId: number, documentId: number, data: { name: string; fileUrl: string; fileType?: string; fileSize?: string }) {
  return requestClient.put<DramaManagementApi.OperationResponse>(`/admin/dramas/${dramaId}/documents/${documentId}`, data);
}

export async function deleteDramaDocument(dramaId: number, documentId: number) {
  return requestClient.delete<DramaManagementApi.OperationResponse>(`/admin/dramas/${dramaId}/documents/${documentId}`);
}

export async function uploadDramaDocument(dramaId: number, file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return requestClient.post<{
    success: boolean;
    message: string;
    data: {
      name: string;
      fileUrl: string;
      fileType: string;
      fileSize: string;
      originalName: string;
      filename: string;
      size: number;
    };
  }>(`/admin/dramas/${dramaId}/documents/upload`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 重新导出类型
export type { DramaManagementApi };
export * from './types';
