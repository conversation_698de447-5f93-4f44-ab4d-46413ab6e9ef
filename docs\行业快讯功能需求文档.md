# 剧投投行业快讯功能需求文档

## 1. 项目背景

### 1.1 现状分析
剧投投官网首页的"行业快讯"模块目前使用硬编码的模拟数据，无法提供真实、及时的行业资讯。为了提升用户体验，增强平台的专业性和权威性，需要开发一套完整的新闻内容管理系统。

### 1.2 业务价值
- **提升用户粘性**：通过高质量的行业资讯吸引用户定期访问
- **建立行业权威**：成为短剧投资领域的信息中心
- **增强SEO效果**：丰富的内容有助于搜索引擎优化
- **支持营销推广**：为公司动态和项目宣传提供渠道

## 2. 功能目标

### 2.1 核心目标
- 建立完整的新闻内容管理系统
- 实现前台展示和后台管理的完整闭环
- 提供优质的用户阅读体验
- 支持SEO优化和社交分享

### 2.2 用户群体
- **前台用户**：投资人、制作方、行业从业者
- **后台用户**：内容编辑、管理员、审核员

## 3. 功能需求

### 3.1 前台展示功能

#### 3.1.1 首页快讯模块
- **分类导航**：行业动态、投资资讯、项目更新、政策法规
- **新闻列表**：卡片式布局，显示封面、标题、摘要、发布时间、阅读量
- **分页加载**：支持分页或无限滚动
- **响应式设计**：适配PC端和移动端

#### 3.1.2 新闻详情页面
- **文章内容**：支持富文本显示，包括图片、视频、链接
- **文章信息**：标题、作者、发布时间、分类、标签
- **互动功能**：阅读量统计、分享功能
- **相关推荐**：推荐相关文章

#### 3.1.3 新闻列表页面
- **高级筛选**：按分类、时间、关键词筛选
- **排序功能**：按时间、热度排序
- **搜索功能**：全文搜索支持
- **SEO优化**：友好的URL结构、meta标签

### 3.2 后台管理功能

#### 3.2.1 新闻管理
- **新闻列表**：表格展示，支持搜索、筛选、排序
- **新闻编辑**：富文本编辑器，支持图片上传、格式设置
- **状态管理**：草稿、待审核、已发布、已下线
- **批量操作**：批量发布、删除、修改状态

#### 3.2.2 分类管理
- **分类设置**：创建、编辑、删除分类
- **层级管理**：支持多级分类结构
- **排序设置**：自定义分类显示顺序

#### 3.2.3 媒体管理
- **图片上传**：支持多图上传、图片压缩
- **文件管理**：文件分类、存储管理
- **CDN集成**：支持云存储和CDN加速

## 4. 技术方案

### 4.1 技术架构

#### 4.1.1 数据库设计
```sql
-- 新闻主表
CREATE TABLE news (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL COMMENT '标题',
  summary TEXT COMMENT '摘要',
  content LONGTEXT COMMENT '正文内容',
  cover_image_url VARCHAR(500) COMMENT '封面图片',
  category_id INT COMMENT '分类ID',
  author VARCHAR(100) COMMENT '作者',
  source_url VARCHAR(500) COMMENT '来源链接',
  status ENUM('draft', 'pending', 'published', 'archived') DEFAULT 'draft',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
  view_count INT DEFAULT 0 COMMENT '阅读量',
  publish_date DATETIME COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category_id),
  INDEX idx_status (status),
  INDEX idx_publish_date (publish_date)
);

-- 分类表
CREATE TABLE news_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  slug VARCHAR(100) UNIQUE COMMENT 'URL别名',
  description TEXT COMMENT '分类描述',
  parent_id INT DEFAULT NULL COMMENT '父分类ID',
  sort_order INT DEFAULT 0 COMMENT '排序',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_parent (parent_id),
  INDEX idx_sort (sort_order)
);

-- 标签表
CREATE TABLE news_tags (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 新闻标签关联表
CREATE TABLE news_tag_relations (
  news_id INT,
  tag_id INT,
  PRIMARY KEY (news_id, tag_id),
  FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES news_tags(id) ON DELETE CASCADE
);
```

#### 4.1.2 API接口设计

**前台接口**
- `GET /api/public/news` - 获取新闻列表
- `GET /api/public/news/:id` - 获取新闻详情
- `GET /api/public/news/categories` - 获取分类列表
- `POST /api/public/news/:id/view` - 增加阅读量

**后台接口**
- `GET /api/admin/news` - 管理员获取新闻列表
- `POST /api/admin/news` - 创建新闻
- `PUT /api/admin/news/:id` - 更新新闻
- `DELETE /api/admin/news/:id` - 删除新闻
- `POST /api/admin/news/:id/publish` - 发布新闻
- `POST /api/admin/upload` - 文件上传

### 4.2 前端实现

#### 4.2.1 Vue3组件设计
```
components/
├── News/
│   ├── NewsCard.vue          # 新闻卡片组件
│   ├── NewsList.vue          # 新闻列表组件
│   ├── NewsDetail.vue        # 新闻详情组件
│   ├── CategoryNav.vue       # 分类导航组件
│   └── NewsSearch.vue        # 搜索组件
```

#### 4.2.2 状态管理
```typescript
// stores/news.ts
export const useNewsStore = defineStore('news', {
  state: () => ({
    newsList: [],
    categories: [],
    currentCategory: 'all',
    loading: false,
    pagination: {
      page: 1,
      pageSize: 12,
      total: 0
    }
  }),
  actions: {
    async fetchNews(params) { /* ... */ },
    async fetchNewsDetail(id) { /* ... */ },
    async fetchCategories() { /* ... */ }
  }
})
```

### 4.3 后台管理实现

#### 4.3.1 管理界面设计
- **新闻列表页**：使用Ant Design Table组件
- **新闻编辑页**：集成富文本编辑器（如TinyMCE或Quill）
- **图片上传**：支持拖拽上传、预览、裁剪

#### 4.3.2 权限控制
```typescript
// 角色权限定义
enum NewsPermission {
  VIEW = 'news:view',
  CREATE = 'news:create',
  EDIT = 'news:edit',
  DELETE = 'news:delete',
  PUBLISH = 'news:publish'
}
```

## 5. 实施计划

### 5.1 开发阶段

#### 第一阶段：数据库和后端API（3天）
- [ ] 数据库表结构设计和创建
- [ ] 后端API接口开发
- [ ] 数据验证和权限控制
- [ ] API文档编写

#### 第二阶段：前端展示功能（2天）
- [ ] 新闻列表组件开发
- [ ] 新闻详情页面开发
- [ ] 分类筛选功能
- [ ] 响应式布局优化

#### 第三阶段：后台管理功能（4天）
- [ ] 新闻管理界面开发
- [ ] 富文本编辑器集成
- [ ] 图片上传功能
- [ ] 分类管理功能

#### 第四阶段：测试和优化（1天）
- [ ] 功能测试
- [ ] 性能优化
- [ ] SEO优化
- [ ] 部署上线

### 5.2 时间安排
- **总开发周期**：10个工作日
- **开发人员**：1名全栈工程师
- **测试人员**：1名QA工程师
- **预计上线时间**：开发完成后1周内

## 6. 验收标准

### 6.1 功能验收
- [ ] 前台新闻展示功能完整
- [ ] 后台管理功能完整
- [ ] 响应式设计适配良好
- [ ] SEO优化效果明显

### 6.2 性能验收
- [ ] 页面加载时间 < 2秒
- [ ] 图片加载优化
- [ ] 数据库查询优化
- [ ] 缓存机制有效

### 6.3 安全验收
- [ ] 输入数据验证
- [ ] XSS攻击防护
- [ ] SQL注入防护
- [ ] 权限控制有效

## 7. 风险评估

### 7.1 技术风险
- **富文本编辑器兼容性**：选择成熟的编辑器解决方案
- **图片上传性能**：使用CDN和图片压缩技术
- **SEO优化效果**：遵循最佳实践，定期监控效果

### 7.2 业务风险
- **内容质量控制**：建立内容审核流程
- **更新频率保证**：制定内容发布计划
- **用户接受度**：收集用户反馈，持续优化

## 8. 后续规划

### 8.1 功能扩展
- **评论系统**：用户评论和互动功能
- **订阅功能**：邮件订阅和推送通知
- **社交分享**：集成社交媒体分享
- **数据分析**：阅读统计和用户行为分析

### 8.2 内容运营
- **内容策略**：制定内容发布策略
- **SEO优化**：持续优化搜索引擎表现
- **用户增长**：通过优质内容吸引用户
- **品牌建设**：建立行业影响力

---

**文档版本**：v1.0  
**创建时间**：2024年8月  
**负责人**：技术团队  
**审核人**：产品经理
