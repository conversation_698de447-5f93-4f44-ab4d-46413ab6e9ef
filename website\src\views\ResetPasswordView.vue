<template>
  <div class="min-h-screen flex items-start justify-center bg-gray-50 pt-20">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden z-0">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-10 rounded-full"></div>
      <div class="absolute top-1/4 -left-20 w-60 h-60 bg-secondary opacity-10 rounded-full"></div>
      <div class="absolute bottom-20 right-1/3 w-40 h-40 bg-primary opacity-10 rounded-full"></div>
    </div>
    
    <div class="relative z-10 bg-white rounded-xl shadow-xl p-8 w-full max-w-md">
      <!-- Logo -->
      <div class="flex justify-center mb-6">
        <div class="flex items-center">
          <svg class="h-10 w-auto mr-2" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#8667F0" />
                <stop offset="100%" stop-color="#6039E4" />
              </linearGradient>
            </defs>
            <rect width="100" height="100" rx="20" fill="url(#logoGradient)"/>
            <path d="M30 70V30H45C50.5228 30 55 34.4772 55 40C55 45.5228 50.5228 50 45 50H35V70H30Z" fill="white"/>
            <path d="M60 30H65V70H60V30Z" fill="white"/>
            <path d="M75 30H80V70H75V30Z" fill="white"/>
          </svg>
          <span class="text-xl font-bold text-gradient">剧投投</span>
        </div>
      </div>
      
      <!-- Header: Title and Subtitle -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">重置密码</h1>
        <p class="text-gray-600">请设置您的新密码</p>
      </div>

      <div v-if="!resetSuccess">
        <Form @submit="handleResetPassword" class="space-y-6" v-slot="{ errors }">
          <div>
            <Field 
              name="password" 
              type="password" 
              v-model="password" 
              rules="required|min:8"
              class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
              placeholder="新密码 (至少8位)"
              :class="{ 'border-red-500': errors.password }"
            />
            <ErrorMessage name="password" class="text-red-500 text-sm mt-1" />
          </div>

          <div>
            <Field 
              name="confirmPassword" 
              type="password" 
              v-model="confirmPassword" 
              rules="required|confirmed:@password"
              class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
              placeholder="确认新密码"
              :class="{ 'border-red-500': errors.confirmPassword }"
            />
            <ErrorMessage name="confirmPassword" class="text-red-500 text-sm mt-1" />
          </div>
          
          <!-- 错误消息显示 -->
          <div v-if="errorMessage" class="text-red-500 text-sm">
            {{ errorMessage }}
          </div>
          
          <!-- Submit Button -->
          <button 
            type="submit" 
            class="w-full bg-gradient-primary text-white font-medium py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-70 disabled:cursor-not-allowed"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting">处理中...</span>
            <span v-else>重置密码</span>
          </button>
        </Form>
      </div>

      <!-- 重置成功显示 -->
      <div v-else class="text-center space-y-6">
        <div class="bg-green-50 text-green-700 p-4 rounded-lg">
          <svg class="w-16 h-16 mx-auto mb-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <p class="text-lg font-medium">密码重置成功!</p>
          <p class="mt-2">您可以使用新密码登录您的账户</p>
        </div>
        
        <button 
          @click="goToLogin" 
          class="w-full bg-gradient-primary text-white font-medium py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none"
        >
          返回登录
        </button>
      </div>

      <!-- Footer -->
      <div class="mt-10 text-center">
        <p class="text-xs text-gray-500">剧投投 - 创造精彩，共享未来</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import { getCompleteApiUrl } from '../utils/environmentConfig';
import { Form, Field, ErrorMessage } from 'vee-validate';

const router = useRouter();
const route = useRoute();
const token = ref('');
const password = ref('');
const confirmPassword = ref('');
const isSubmitting = ref(false);
const errorMessage = ref('');
const resetSuccess = ref(false);

// 获取API基础URL
const apiUrl = getCompleteApiUrl();

onMounted(() => {
  // 从URL获取重置令牌
  token.value = route.params.token || '';
  
  if (!token.value) {
    errorMessage.value = '无效的密码重置链接，请重新申请';
  }
});

const handleResetPassword = async () => {
  try {
    if (!token.value) {
      errorMessage.value = '无效的密码重置链接，请重新申请';
      return;
    }
    
    if (password.value !== confirmPassword.value) {
      errorMessage.value = '两次输入的密码不一致';
      return;
    }
    
    isSubmitting.value = true;
    errorMessage.value = '';
    
    // 调用后端API
    const response = await axios.post(`${apiUrl}/auth/reset-password`, {
      token: token.value,
      password: password.value
    });
    
    if (response.data.success) {
      resetSuccess.value = true;
    }
  } catch (error) {
    console.error('重置密码失败:', error);
    
    if (error.response) {
      errorMessage.value = error.response.data.message || '重置密码失败，请稍后重试';
    } else {
      errorMessage.value = '网络错误，无法连接到服务器';
    }
  } finally {
    isSubmitting.value = false;
  }
};

const goToLogin = () => {
  router.push('/login');
};
</script>

<style scoped>
.text-gradient {
  @apply text-primary font-bold;
}

.bg-gradient-primary {
  @apply bg-primary hover:bg-primary-dark;
}
</style> 