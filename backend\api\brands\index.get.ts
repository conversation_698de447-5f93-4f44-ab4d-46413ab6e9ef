import { query } from '~/utils/database'

/**
 * 获取公开厂牌列表接口
 * GET /api/brands
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query_params = getQuery(event)
    const { limit = 20 } = query_params

    // 限制返回数量
    const limitNum = Math.min(Number(limit) || 20, 100)

    // 查询启用状态的厂牌，只返回必要字段
    const brands = await query(
      `SELECT 
        id,
        brand_name,
        brand_logo,
        company_name,
        created_at
      FROM brands 
      WHERE status = 'active'
      ORDER BY created_at DESC 
      LIMIT ?`,
      [limitNum]
    ) as any[]

    // 格式化厂牌数据
    const formattedBrands = brands.map((brand: any) => ({
      id: brand.id,
      name: brand.brand_name,
      logo: brand.brand_logo,
      companyName: brand.company_name,
      createdAt: brand.created_at
    }))

    return {
      success: true,
      data: formattedBrands,
      total: formattedBrands.length
    }

  } catch (error: any) {
    console.error('获取厂牌列表失败:', error)
    
    throw createError({
      statusCode: 500,
      statusMessage: '获取厂牌列表失败'
    })
  }
})
