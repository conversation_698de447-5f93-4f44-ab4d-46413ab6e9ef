<template>
  <!-- 短剧编辑卡片 -->
  <div class="card-box p-6">
    <!-- 加载中状态 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <Spin />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-500 mb-4">{{ error }}</div>
      <AButton @click="loadDramaDetail">重新加载</AButton>
    </div>

    <!-- 短剧编辑内容 -->
    <template v-else-if="drama">
      <!-- 短剧标题和基本信息 -->
      <div class="mb-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h1 class="text-2xl font-bold mb-2">
              编辑短剧：{{ drama.title }}
              <span class="text-lg text-gray-500 font-normal ml-2">(ID: {{ drama.id }})</span>
            </h1>
          </div>
          <div class="flex space-x-2">
            <AButton @click="goBack" type="default">
              <template #icon>
                <ArrowLeft class="size-4" />
              </template>
              返回短剧列表
            </AButton>
            <AButton @click="handleSaveAll" type="primary" :loading="saving">
              <template #icon>
                <Save class="size-4" />
              </template>
              保存所有更改
            </AButton>
          </div>
        </div>
      </div>

      <!-- 编辑导航 -->
      <div class="border-b border-gray-200 mb-6">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.key
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            {{ tab.name }}
            <span v-if="hasUnsavedChanges(tab.key)" class="ml-1 text-orange-500">*</span>
          </button>
        </nav>
      </div>

      <!-- 编辑内容区域 -->
      <!-- 基本信息 -->
      <div v-if="activeTab === 'basic'">
        <BasicInfoEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('basic')"
          @data-changed="handleDataChanged('basic', $event)"
        />
      </div>

      <!-- 剧本和计划书 -->
      <div v-if="activeTab === 'documents'">
        <DocumentsInfoEdit
          :drama-id="drama.id"
          :documents="drama.documents || []"
          @save-success="handleTabSaveSuccess('documents')"
          @data-changed="handleDataChanged('documents', $event)"
        />
      </div>

      <!-- 创作团队 -->
      <div v-if="activeTab === 'team'">
        <TeamInfoEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('team')"
          @data-changed="handleDataChanged('team', $event)"
        />
      </div>



      <!-- 募资详情 -->
      <div v-if="activeTab === 'funding'">
        <FundingInfoEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('funding')"
          @data-changed="handleDataChanged('funding', $event)"
        />
      </div>

      <!-- 投资权益 -->
      <div v-if="activeTab === 'investment'">
        <InvestmentTiersEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('investment')"
          @data-changed="handleDataChanged('investment', $event)"
        />
      </div>

      <!-- 制作排期 -->
      <div v-if="activeTab === 'schedule'">
        <ScheduleInfoEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('schedule')"
          @data-changed="handleDataChanged('schedule', $event)"
        />
      </div>

      <!-- 项目素材 -->
      <div v-if="activeTab === 'materials'">
        <MaterialsInfoEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('materials')"
          @data-changed="handleDataChanged('materials', $event)"
        />
      </div>

      <!-- 其他信息 -->
      <div v-if="activeTab === 'other'">
        <OtherInfoEdit
          :drama="drama"
          @save-success="handleTabSaveSuccess('other')"
          @data-changed="handleDataChanged('other', $event)"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from 'vue';

import { Spin, Button as AButton, message } from 'ant-design-vue';
import { ArrowLeft, Save } from '@vben/icons';
import { getDramaDetail } from '#/api/drama-management';

// 导入编辑组件
import BasicInfoEdit from './components/edit/BasicInfoEdit.vue';
import MaterialsInfoEdit from './components/edit/MaterialsInfoEdit.vue';
import DocumentsInfoEdit from './components/edit/DocumentsInfoEdit.vue';
import TeamInfoEdit from './components/edit/TeamInfoEdit.vue';
import ScheduleInfoEdit from './components/edit/ScheduleInfoEdit.vue';
import FundingInfoEdit from './components/edit/FundingInfoEdit.vue';
import InvestmentTiersEdit from './components/edit/InvestmentTiersEdit.vue';
import OtherInfoEdit from './components/edit/OtherInfoEdit.vue';

// 定义props
interface Props {
  dramaId: number | null;
}

const props = defineProps<Props>();

// 定义emits
const emit = defineEmits<{
  backToList: [];
}>();

// 状态
const loading = ref(false);
const error = ref('');
const drama = ref(null);
const activeTab = ref('basic');
const saving = ref(false);

// 跟踪各个选项卡的更改状态
const tabChanges = reactive<Record<string, boolean>>({
  basic: false,
  materials: false,
  documents: false,
  team: false,
  schedule: false,
  funding: false,
  investment: false,
  other: false,
});

// 标签页配置
const tabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'materials', name: '项目素材' },
  { key: 'documents', name: '剧本和计划书' },
  { key: 'team', name: '创作团队' },
  { key: 'schedule', name: '制作排期' },
  { key: 'funding', name: '募资详情' },
  { key: 'investment', name: '投资权益' },
  { key: 'other', name: '其他信息' },
];

// 加载短剧详情
const loadDramaDetail = async () => {
  if (!props.dramaId) {
    error.value = '短剧ID不能为空';
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    error.value = '';

    const response = await getDramaDetail(props.dramaId);

    // 由于响应拦截器已经处理了数据格式，response 直接是 data 部分
    drama.value = response;

    // 重置更改状态
    Object.keys(tabChanges).forEach(key => {
      tabChanges[key] = false;
    });
  } catch (err: any) {
    console.error('加载短剧详情失败:', err);
    error.value = err.message || '加载短剧详情失败';
  } finally {
    loading.value = false;
  }
};

// 返回短剧列表
const goBack = () => {
  // 检查是否有未保存的更改
  const hasChanges = Object.values(tabChanges).some(changed => changed);
  if (hasChanges) {
    if (confirm('您有未保存的更改，确定要离开吗？')) {
      emit('backToList');
    }
  } else {
    emit('backToList');
  }
};

// 检查选项卡是否有未保存的更改
const hasUnsavedChanges = (tabKey: string) => {
  return tabChanges[tabKey];
};

// 处理选项卡保存成功
const handleTabSaveSuccess = (tabKey: string) => {
  tabChanges[tabKey] = false;
  message.success(`${tabs.find(t => t.key === tabKey)?.name}保存成功`);

  // 重新加载数据以获取最新状态
  loadDramaDetail();
};

// 处理数据更改
const handleDataChanged = (tabKey: string, hasChanges: boolean) => {
  tabChanges[tabKey] = hasChanges;
};

// 保存所有更改
const handleSaveAll = async () => {
  try {
    saving.value = true;

    // 这里可以实现批量保存逻辑
    // 目前先提示用户分别保存各个选项卡
    const changedTabs = Object.entries(tabChanges)
      .filter(([_, changed]) => changed)
      .map(([key, _]) => tabs.find(t => t.key === key)?.name)
      .filter(Boolean);

    if (changedTabs.length === 0) {
      message.info('没有需要保存的更改');
      return;
    }

    message.info(`请分别保存以下选项卡的更改：${changedTabs.join('、')}`);
  } catch (error: any) {
    message.error(error.message || '保存失败');
  } finally {
    saving.value = false;
  }
};

// 监听dramaId变化
watch(() => props.dramaId, (newDramaId) => {
  if (newDramaId) {
    loadDramaDetail();
  }
}, { immediate: true });

onMounted(() => {
  if (props.dramaId) {
    loadDramaDetail();
  }
});
</script>

<style scoped>
/* 让短剧编辑页面的字体更黑 */
.card-box {
  color: #1f2937; /* text-gray-800 */
}

.card-box h1,
.card-box h2,
.card-box h3,
.card-box h4 {
  color: #111827; /* text-gray-900 */
}

/* 表单项标签字体加深 */
:deep(.ant-form-item-label > label) {
  color: #374151 !important; /* text-gray-700 */
  font-weight: 500;
}

/* 导航标签文字加深 */
.card-box nav button {
  color: #374151; /* text-gray-700 */
}

.card-box nav button:hover {
  color: #111827; /* text-gray-900 */
}

/* 未保存更改的标识 */
.card-box nav button .text-orange-500 {
  font-weight: bold;
}
</style>
