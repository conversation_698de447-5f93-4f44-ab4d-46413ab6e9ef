# 剧投投后端服务

基于 Nitro 框架的现代化后端服务，提供完整的短剧投资平台 API。

## 项目结构

```
backend/
├── api/                    # API 路由
│   ├── actors/            # 演员相关 API
│   ├── admin/             # 管理员 API
│   │   ├── actors/        # 管理员-演员管理
│   │   ├── audit-logs/    # 管理员-审计日志
│   │   ├── banners/       # 管理员-横幅管理
│   │   ├── dramas/        # 管理员-短剧管理
│   │   ├── funds/         # 管理员-基金管理
│   │   ├── menus/         # 管理员-菜单管理
│   │   ├── settings/      # 管理员-系统设置
│   │   ├── upload/        # 管理员-文件上传
│   │   └── user-management/ # 管理员-用户管理
│   ├── auth/              # 认证相关 API
│   │   └── admin/         # 管理员认证
│   ├── dramas/            # 短剧相关 API
│   ├── funds/             # 基金相关 API
│   ├── public/            # 公开 API
│   │   └── settings/      # 公开设置
│   ├── users/             # 用户相关 API
│   └── health.get.ts      # 健康检查
├── middleware/            # 中间件
├── routes/               # 路由配置
├── scripts/              # 脚本文件
├── types/                # TypeScript 类型定义
├── utils/                # 工具函数
├── .env                  # 环境变量配置
├── nitro.config.ts       # Nitro 配置
└── package.json          # 项目依赖
```

## 技术栈

- **框架**: Nitro (UnJS 生态)
- **语言**: TypeScript
- **数据库**: MySQL 8.0+
- **认证**: JWT
- **文件上传**: Multer + 云存储支持
- **日志**: 自定义日志系统
- **部署**: 支持多种部署方式

## 功能特性

- ✅ 用户认证和权限管理
- ✅ 基金产品管理
- ✅ 轮播图管理
- ✅ 艺人管理
- ✅ 系统设置
- ✅ 审计日志
- ✅ 文件上传
- ✅ 数据分页和搜索

## 快速开始

### 环境要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- pnpm >= 8.0.0

### 安装依赖

```bash
pnpm install
```

### 配置环境

复制 `.env.example` 到 `.env` 并配置相关参数：

```bash
cp .env.example .env
```

### 数据库初始化

执行 SQL 脚本初始化数据库结构和基础数据。

### 启动服务

```bash
# 开发模式
pnpm run dev

# 生产模式
pnpm run build
pnpm run preview
```

## API 文档

服务启动后访问 `http://localhost:3001` 查看可用的 API 接口列表。

### 主要接口

- `POST /api/auth/login` - 用户登录
- `GET /api/auth/profile` - 获取用户信息
- `GET /api/admin/users` - 用户管理
- `GET /api/admin/funds` - 基金管理
- `GET /api/banners` - 轮播图管理
- `GET /api/actors` - 艺人管理
