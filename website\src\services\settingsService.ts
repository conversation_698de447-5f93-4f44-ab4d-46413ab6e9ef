// src/services/settingsService.ts
// 网站设置服务

import axios, { AxiosResponse } from 'axios'
import axiosInstance from '../utils/axiosConfig'
import { getApiBaseUrl } from '../utils/environmentConfig'
import type { ApiResponse } from '../types'

// 网站设置类型定义
export interface SiteSettings {
  title: string
  description: string
  logo: string
  favicon: string
  icp: string
  copyright: string
}

// 投资设置类型定义
export interface InvestmentSettings {
  minAmount: number
  maxAmount: number
  minReturnRate: number
  platformFee: number
}

// 所有公开设置类型
export interface AllPublicSettings {
  site_settings: SiteSettings
  investment_settings: InvestmentSettings
}

// 设置缓存类型
interface SettingsCache {
  site: SiteSettings | null
  investment: InvestmentSettings | null
  lastFetchTime: number
}

// 设置缓存
let settingsCache: SettingsCache = {
  site: null,
  investment: null,
  lastFetchTime: 0
}

// 缓存过期时间（毫秒）
const CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟

/**
 * 获取网站设置
 * @returns 包含网站设置的Promise
 */
export const getSiteSettings = async (): Promise<SiteSettings> => {
  try {
    // 检查缓存是否有效
    const now = Date.now()
    if (settingsCache.site && now - settingsCache.lastFetchTime < CACHE_EXPIRY) {
      return settingsCache.site
    }
    
    // 从API获取最新设置 - 移除/api前缀，因为axiosInstance的baseURL已包含
    const response: AxiosResponse<ApiResponse<SiteSettings>> = await axiosInstance.get('/public/settings/site')
    
    if (response.data && response.data.success) {
      // 更新缓存
      settingsCache.site = response.data.data
      settingsCache.lastFetchTime = now
      return response.data.data
    }
    
    throw new Error('获取网站设置失败')
  } catch (error) {
    console.error('获取网站设置出错:', error)
    
    // 如果缓存存在但过期，仍然返回缓存数据作为后备
    if (settingsCache.site) {
      return settingsCache.site
    }
    
    // 返回默认值
    return {
      title: '剧投投',
      description: '中国最专业的短剧投资平台',
      logo: '/images/jutoutou-logo.svg',
      favicon: '/favicon.ico',
      icp: '粤ICP备XXXXXXXX号',
      copyright: '© 2023 剧投投. 保留所有权利'
    }
  }
}

/**
 * 获取投资设置
 * @returns 包含投资设置的Promise
 */
export const getInvestmentSettings = async (): Promise<InvestmentSettings> => {
  try {
    // 检查缓存是否有效
    const now = Date.now()
    if (settingsCache.investment && now - settingsCache.lastFetchTime < CACHE_EXPIRY) {
      return settingsCache.investment
    }
    
    // 从API获取所有公开设置 - 移除/api前缀，因为axiosInstance的baseURL已包含
    const response: AxiosResponse<ApiResponse<AllPublicSettings>> = await axiosInstance.get('/public/settings')
    
    if (response.data && response.data.success && response.data.data.investment_settings) {
      // 更新缓存
      settingsCache.site = response.data.data.site_settings
      settingsCache.investment = response.data.data.investment_settings
      settingsCache.lastFetchTime = now
      return response.data.data.investment_settings
    }
    
    throw new Error('获取投资设置失败')
  } catch (error) {
    console.error('获取投资设置出错:', error)
    
    // 如果缓存存在但过期，仍然返回缓存数据作为后备
    if (settingsCache.investment) {
      return settingsCache.investment
    }
    
    // 返回默认值
    return {
      minAmount: 10000,
      maxAmount: 1000000,
      minReturnRate: 8,
      platformFee: 2
    }
  }
}

/**
 * 获取所有公开设置
 * @returns 包含所有公开设置的Promise
 */
export const getAllSettings = async (): Promise<AllPublicSettings> => {
  try {
    // 检查缓存是否有效
    const now = Date.now()
    if (settingsCache.site && settingsCache.investment && now - settingsCache.lastFetchTime < CACHE_EXPIRY) {
      return {
        site_settings: settingsCache.site,
        investment_settings: settingsCache.investment
      }
    }
    
    // 从API获取所有公开设置 - 移除/api前缀，因为axiosInstance的baseURL已包含
    const response: AxiosResponse<ApiResponse<AllPublicSettings>> = await axiosInstance.get('/public/settings')
    
    if (response.data && response.data.success) {
      // 更新缓存
      settingsCache.site = response.data.data.site_settings
      settingsCache.investment = response.data.data.investment_settings
      settingsCache.lastFetchTime = now
      return response.data.data
    }
    
    throw new Error('获取公开设置失败')
  } catch (error) {
    console.error('获取公开设置出错:', error)
    
    // 如果缓存存在但过期，仍然返回缓存数据作为后备
    if (settingsCache.site && settingsCache.investment) {
      return {
        site_settings: settingsCache.site,
        investment_settings: settingsCache.investment
      }
    }
    
    // 返回默认值
    return {
      site_settings: {
        title: '剧投投',
        description: '中国最专业的短剧投资平台',
        logo: '/images/jutoutou-logo.svg',
        favicon: '/favicon.ico',
        icp: '粤ICP备XXXXXXXX号',
        copyright: '© 2023 剧投投. 保留所有权利'
      },
      investment_settings: {
        minAmount: 10000,
        maxAmount: 1000000,
        minReturnRate: 8,
        platformFee: 2
      }
    }
  }
}

/**
 * 使用系统设置更新文档标题和图标
 */
export const updateDocumentMetadata = async (): Promise<void> => {
  try {
    const siteSettings = await getSiteSettings()
    
    // 更新页面标题
    if (siteSettings.title) {
      document.title = siteSettings.title
    }
    
    // 更新favicon
    if (siteSettings.favicon) {
      const faviconLink = document.querySelector("link[rel*='icon']") as HTMLLinkElement || document.createElement('link')
      faviconLink.type = 'image/x-icon'
      faviconLink.rel = 'shortcut icon'
      faviconLink.href = siteSettings.favicon
      document.getElementsByTagName('head')[0].appendChild(faviconLink)
    }
  } catch (error) {
    console.error('更新文档元数据失败:', error)
  }
}

export default {
  getSiteSettings,
  getInvestmentSettings,
  getAllSettings,
  updateDocumentMetadata
}
