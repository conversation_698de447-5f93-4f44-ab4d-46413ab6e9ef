<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import HomeBanner from '../components/HomeBanner.vue'
import DramaTag from '../components/common/DramaTag.vue'
import { getPublicDramas, getPublicPlatforms, type Platform } from '../api/dramaService'
import { getWebsiteStats, type WebsiteStats } from '../api/websiteAPI'
import { useTags } from '../composables/useTags'
import { useRouter } from 'vue-router'
import type { Drama } from '../types'

const router = useRouter()

// 标签管理
const { loadTags, tagMap, parseTagData } = useTags()



// 工具提示当前活跃ID
const activeTooltip = ref(null)

// 显示工具提示
const showTooltip = (id: any) => {
  activeTooltip.value = id
}

// 隐藏工具提示
const hideTooltip = () => {
  activeTooltip.value = null
}



// 数据状态
const dramas = ref<Drama[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// 网站统计数据
const websiteStats = ref<WebsiteStats | null>(null)
const statsLoading = ref(false)
const statsError = ref<string | null>(null)



// 项目专管员联系弹窗状态
const showContactModal = ref(false)

// 实时投资人数（在1575-1685之间随机变化）
const realtimeInvestors = ref(1575)
const currentUserIndex = ref(0)

// 定时器引用
let investorTimer: NodeJS.Timeout | null = null
let userTimer: NodeJS.Timeout | null = null

const highlights = ref([
  {
    id: 1,
    title: "丰富项目资源",
    description: "汇聚优质短剧IP项目库，精选高潜力影视作品，多元化投资选择降低风险",
    roi: "500+",
    icon: 'chart-pie',
    color: 'bg-blue-500'
  },
  {
    id: 2,
    title: "专业风险控制",
    description: "完善的项目评估体系，资深团队严格把关，多重保障机制确保投资安全",
    roi: "AAA",
    icon: 'shield-check',
    color: 'bg-green-500'
  },
  {
    id: 3,
    title: "透明收益分配",
    description: "公开透明的收益分配机制，定期结算投资回报，让每一分收益都清晰可见",
    roi: "100%",
    icon: 'trending-up',
    color: 'bg-purple-500'
  },
  {
    id: 4,
    title: "资深团队支持",
    description: "行业资深专家团队，丰富的影视投资经验，全程专业指导和服务支持",
    roi: "10年+",
    icon: 'users',
    color: 'bg-orange-500'
  }
])

// 募资人说数据
const fundraiserReviews = ref([
  {
    id: 1,
    name: "张总",
    company: "星光影视CEO",
    review: "平台募资效率非常高，我们的项目在3天内就完成了目标金额的80%，投资人对接速度很快，服务团队专业贴心。",
    rating: 4.8
  },
  {
    id: 2,
    name: "李导",
    company: "梦想工作室创始人",
    review: "通过剧投投平台，我们成功募集到500万资金，整个流程透明高效，平台的推广支持让我们的项目获得了更多关注。",
    rating: 4.9
  },
  {
    id: 3,
    name: "王制片",
    company: "华彩传媒制片人",
    review: "募资频次高，资金到位快，平台的专业服务让我们专注于内容创作，不用担心资金问题，强烈推荐给同行。",
    rating: 5.0
  },
  {
    id: 4,
    name: "陈总监",
    company: "青春影业投资总监",
    review: "平台的投资人质量很高，都是真正懂短剧行业的专业投资者，沟通效率高，决策速度快，合作非常愉快。",
    rating: 4.7
  },
  {
    id: 5,
    name: "刘编剧",
    company: "原创工坊编剧",
    review: "作为独立编剧，通过平台找到了合适的投资方，募资金额超出预期，平台的服务团队给了我们很多专业建议。",
    rating: 4.6
  },
  {
    id: 6,
    name: "赵总",
    company: "新锐传媒CEO",
    review: "平台的募资速度让人惊喜，我们的项目在一周内就达到了募资目标，投资人的专业度和积极性都很高。",
    rating: 4.8
  },
  {
    id: 7,
    name: "孙导演",
    company: "独立导演",
    review: "第一次使用就被平台的专业服务震撼了，从项目包装到投资人对接，每个环节都有专人跟进，效率极高。",
    rating: 4.9
  },
  {
    id: 8,
    name: "周制片",
    company: "飞扬影视制片人",
    review: "平台的投资人资源丰富，我们的项目得到了多家投资机构的关注，最终选择了最合适的合作伙伴。",
    rating: 4.5
  },
  {
    id: 9,
    name: "吴总",
    company: "创意无限CEO",
    review: "募资流程简单透明，资金管理规范，平台的风控体系让投资人更有信心，我们的募资成功率大大提升。",
    rating: 4.7
  },
  {
    id: 10,
    name: "马导",
    company: "新生代导演",
    review: "平台不仅帮我们募集到了资金，还提供了很多行业资源和专业指导，真正做到了全方位的服务支持。",
    rating: 5.0
  },
  {
    id: 11,
    name: "胡总",
    company: "光影传媒CEO",
    review: "在剧投投平台上，我们的古装短剧项目仅用5天就完成了800万的募资目标，投资人的响应速度超出预期。",
    rating: 4.8
  },
  {
    id: 12,
    name: "林制片",
    company: "青春制作制片人",
    review: "平台的专业度让我印象深刻，从项目评估到资金到位，每个环节都有详细的进度反馈，让我们很安心。",
    rating: 4.6
  },
  {
    id: 13,
    name: "徐导",
    company: "新锐导演",
    review: "作为新人导演，平台给了我很多机会，不仅帮助募资成功，还介绍了很多行业资源，对我的职业发展帮助很大。",
    rating: 4.9
  },
  {
    id: 14,
    name: "何总监",
    company: "华夏影业投资总监",
    review: "平台的风控体系很完善，投资人对我们的项目很有信心，募资过程中没有遇到任何资金安全问题。",
    rating: 4.7
  },
  {
    id: 15,
    name: "郭编剧",
    company: "金牌编剧工作室",
    review: "通过平台认识了很多志同道合的投资人，他们不仅提供资金支持，还给出了很多创作上的建议。",
    rating: 4.5
  },
  {
    id: 16,
    name: "邓总",
    company: "星辰传媒CEO",
    review: "平台的服务效率真的很高，我们的悬疑短剧项目从提交到完成募资只用了一周时间，非常满意。",
    rating: 5.0
  },
  {
    id: 17,
    name: "韩制片",
    company: "梦幻影视制片人",
    review: "募资金额达到了我们的预期目标，平台的推广策略很有效，让我们的项目获得了更多曝光。",
    rating: 4.8
  },
  {
    id: 18,
    name: "冯导",
    company: "独立制片人",
    review: "平台的投资人都很专业，对短剧行业有深入了解，合作过程中给了我们很多宝贵的意见。",
    rating: 4.6
  },
  {
    id: 19,
    name: "曾总",
    company: "飞跃影业CEO",
    review: "第二次在平台募资了，依然很满意。平台的服务质量稳定，投资人资源丰富，值得信赖。",
    rating: 4.9
  },
  {
    id: 20,
    name: "谭制片",
    company: "新时代制作制片人",
    review: "平台的数据分析很专业，帮我们优化了项目方案，最终募资金额比预期高出了30%。",
    rating: 4.7
  },
  {
    id: 21,
    name: "石导",
    company: "青年导演",
    review: "作为年轻导演，平台给了我展示才华的机会，投资人对我的项目很认可，这让我很有成就感。",
    rating: 4.4
  },
  {
    id: 22,
    name: "廖总监",
    company: "盛世传媒投资总监",
    review: "平台的项目匹配度很高，推荐给我们的投资人都很符合我们的需求，节省了大量的沟通成本。",
    rating: 4.8
  },
  {
    id: 23,
    name: "姚编剧",
    company: "原创剧本工作室",
    review: "通过平台不仅完成了募资，还学到了很多行业知识，平台的培训和指导服务很有价值。",
    rating: 4.6
  },
  {
    id: 24,
    name: "贺总",
    company: "光速影视CEO",
    review: "平台的募资速度真的很快，我们的都市情感短剧项目在48小时内就有投资人联系，效率惊人。",
    rating: 4.3
  }
])

// 将评价数据分配到两行，实现无缝循环滚动
const getFirstRowReviews = () => {
  // 复制数据多次以实现无缝循环
  const reviews = [...fundraiserReviews.value, ...fundraiserReviews.value, ...fundraiserReviews.value]
  return reviews
}

const getSecondRowReviews = () => {
  // 从第二个评价开始，错开显示，复制数据多次以实现无缝循环
  const reviews = [...fundraiserReviews.value.slice(1), ...fundraiserReviews.value, ...fundraiserReviews.value, ...fundraiserReviews.value.slice(0, 1)]
  return reviews
}

// 根据评分生成星星显示状态
const getStarDisplay = (rating: number) => {
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 >= 0.5
  const stars = []

  // 添加满星
  for (let i = 0; i < fullStars; i++) {
    stars.push('full')
  }

  // 添加半星
  if (hasHalfStar && fullStars < 5) {
    stars.push('half')
  }

  // 添加空星
  while (stars.length < 5) {
    stars.push('empty')
  }

  return stars
}

const investmentProcess = ref([
  {
    id: 1,
    title: "项目提交审核",
    description: "提交短剧项目资料",
    details: "提交完整的项目策划书、剧本大纲、制作团队信息等资料，平台专业团队进行项目评估和审核",
    icon: "document-text",
    color: "bg-blue-500"
  },
  {
    id: 2,
    title: "募资启动推广",
    description: "项目上线开始募资",
    details: "审核通过后项目正式上线，平台提供专业推广支持，帮助项目快速获得投资人关注",
    icon: "search",
    color: "bg-green-500"
  },
  {
    id: 3,
    title: "资金管理分配",
    description: "募资资金安全托管",
    details: "募集资金由银行第三方托管，按制作进度分批拨付，确保资金安全和合理使用",
    icon: "cash",
    color: "bg-yellow-500"
  },
  {
    id: 4,
    title: "项目制作回报",
    description: "制作完成收益分配",
    details: "项目制作完成上线后，根据实际收益情况进行透明的收益分配，制作方获得相应回报",
    icon: "film",
    color: "bg-purple-500"
  }
])

// 短剧平台数据
const platforms = ref<Platform[]>([])
const platformsLoading = ref(false)
const platformsError = ref('')

// 加载短剧平台数据
const loadPlatforms = async () => {
  try {
    platformsLoading.value = true
    platformsError.value = ''
    const response = await getPublicPlatforms()
    if (response.data && response.data.success) {
      platforms.value = response.data.data
    } else {
      platformsError.value = response.data?.message || '获取平台数据失败'
    }
  } catch (error) {
    console.error('加载平台数据失败:', error)
    platformsError.value = '网络错误，请稍后重试'
  } finally {
    platformsLoading.value = false
  }
}





// 导入新闻store
import { useNewsStore } from '@/store/news'

const newsStore = useNewsStore()
const activeNewsCategory = ref("全部")

// 新闻分类数据
const newsCategories = computed(() => {
  const categories = ["全部"]
  if (newsStore.flatCategories.length > 0) {
    categories.push(...newsStore.flatCategories.map(cat => cat.name))
  }
  return categories
})

// 筛选后的新闻数据
const filteredNews = computed(() => {
  if (activeNewsCategory.value === "全部") {
    return newsStore.featuredNews
  }
  return newsStore.featuredNews.filter(news =>
    news.category?.name === activeNewsCategory.value
  )
})

// 加载短剧数据
const loadDramaData = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await getPublicDramas({
      page: 1,
      pageSize: 12,
      status: 'funding' as any // 只获取募资中的短剧（剩余天数>0）
    })

    if (response.data && response.data.success) {
      dramas.value = response.data.data.list
    } else {
      error.value = '获取短剧数据失败'
    }
  } catch (err) {
    console.error('加载短剧数据失败:', err)
    error.value = '加载短剧数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 加载网站统计数据
const loadWebsiteStats = async () => {
  statsLoading.value = true
  statsError.value = null

  try {
    const response = await getWebsiteStats()

    if (response && response.success && response.data) {
      websiteStats.value = response.data
    } else {
      statsError.value = response?.message || '获取统计数据失败'
    }
  } catch (err) {
    console.error('加载统计数据失败:', err)
    statsError.value = '加载统计数据失败，请稍后重试'
  } finally {
    statsLoading.value = false
  }
}

// 启动实时投资人数变化
const startRealtimeInvestors = () => {
  investorTimer = setInterval(() => {
    // 在1575-1685之间随机变化，使用更自然的变化幅度
    const currentValue = realtimeInvestors.value
    const min = 1575
    const max = 1685

    // 生成相对当前值的小幅变化，使变化更自然
    const changeRange = 10 // 每次变化范围
    const minChange = Math.max(min, currentValue - changeRange)
    const maxChange = Math.min(max, currentValue + changeRange)

    realtimeInvestors.value = Math.floor(Math.random() * (maxChange - minChange + 1)) + minChange
  }, 4000) // 每4秒变化一次
}

// 启动用户轮播
const startUserRotation = () => {
  userTimer = setInterval(() => {
    if (websiteStats.value?.users && websiteStats.value.users.length > 0) {
      currentUserIndex.value = (currentUserIndex.value + 1) % websiteStats.value.users.length
    }
  }, 3000) // 每3秒切换一次用户
}







// 计算属性
const currentUser = computed(() => {
  if (websiteStats.value?.users && websiteStats.value.users.length > 0) {
    return websiteStats.value.users[currentUserIndex.value]
  }
  return null
})

// 计算进度百分比
const calculateProgress = (current: number, goal: number): number => {
  return Math.min(Math.round((current / goal) * 100), 100);
};

// 格式化货币
const formatCurrency = (value: number): string => {
  return (value / 10000).toFixed(0) + ' 万';
};

// 在首页只显示最多12个项目（2行，每行6个）
const displayedProjects = computed(() => {
  return dramas.value.slice(0, 12);
});

// 清理定时器
const clearTimers = () => {
  if (investorTimer) {
    clearInterval(investorTimer)
    investorTimer = null
  }
  if (userTimer) {
    clearInterval(userTimer)
    userTimer = null
  }
}



// 在组件挂载时加载数据
onMounted(() => {
  loadTags() // 加载标签数据
  loadDramaData()
  loadWebsiteStats()
  loadPlatforms() // 加载短剧平台数据
  startRealtimeInvestors()
  startUserRotation()

  // 加载新闻数据
  newsStore.fetchCategories()
  newsStore.fetchFeaturedNews(6)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  clearTimers()
})

// 导航到项目详情页
const navigateToProjectDetail = (projectId: number) => {
  router.push(`/project/${projectId}`);
};

// 处理投资点击
const handleInvestment = (project: Drama) => {
  // 跳转到项目详情页面的投资部分
  router.push(`/project/${project.id}#investment`);
};

// 新闻分类切换
const changeNewsCategory = (category: string) => {
  activeNewsCategory.value = category;
};

// 导航到新闻详情页
const navigateToNewsDetail = (newsId: number) => {
  router.push(`/news/${newsId}`);
};

// 判断是否为最近的新闻（3天内）
const isRecentNews = (publishDate: string) => {
  const newsDate = new Date(publishDate);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - newsDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 3;
};


</script>

<template>
  <div class="home">
    <!-- 轮播图 -->
    <HomeBanner />

    <!-- 指标看板 -->
    <section class="py-8 bg-gradient-to-r from-blue-50 to-purple-50">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 已募资金总额 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <div class="flex items-center">
                <h3 class="text-lg font-medium text-gray-500">已募资金总额</h3>
                <div class="ml-2 flex items-center text-green-500">
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  <span class="text-xs ml-1">{{ websiteStats?.fundingGrowthRate || 36.4 }}%</span>
                </div>
              </div>
              <div class="text-3xl font-bold text-gray-800 mt-1">
                {{ websiteStats?.totalRaisedAmount?.toFixed(2) || '0.00' }} 万元
              </div>
              <div class="text-sm text-gray-500 mt-1">募资金额增长 <span class="text-primary font-medium">{{ websiteStats?.fundingGrowthRate || 36.4 }}%</span></div>
            </div>
          </div>
          
          <!-- 今日投资总额 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-500">今日投资总额</h3>
              <div class="text-2xl font-bold text-gray-800 mt-1">
                {{ websiteStats?.todayInvestmentAmount?.toFixed(2) || '0.00' }} 万 /
                {{ websiteStats?.historicalInvestmentTotal?.toFixed(0) || '0' }} 万
              </div>
              <div class="text-sm text-gray-500 mt-1">
                占比 <span class="text-primary font-medium">{{ websiteStats?.todayInvestmentRatio || 0 }}</span>
              </div>
            </div>
          </div>
          
          <!-- 实时投资人 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-500">实时投资人</h3>
              <div class="text-3xl font-bold text-gray-800 mt-1">
                {{ realtimeInvestors }} <span class="text-sm font-normal">位</span>
              </div>
              <div class="text-sm text-gray-500 mt-1">
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded">实时在线</span>
                <span v-if="currentUser" class="ml-2 text-xs">
                  {{ currentUser.username }} <span class="text-primary">{{ currentUser.userType }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资亮点 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资亮点</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            专业平台资源优势，完善投资保障体系，为投资人提供安全可靠的高收益投资机会
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div 
            v-for="item in highlights" 
            :key="item.id" 
            class="card hover:shadow-lg transition-shadow duration-300 overflow-hidden"
          >
            <div :class="['w-16 h-16 rounded-full flex items-center justify-center text-white mb-6', item.color]">
              <svg v-if="item.icon === 'chart-pie'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
              <svg v-else-if="item.icon === 'users'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <svg v-else-if="item.icon === 'shield-check'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <svg v-else-if="item.icon === 'trending-up'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            
            <h3 class="text-xl font-semibold mb-2">{{ item.title }}</h3>
            <p class="text-gray-600 mb-4">{{ item.description }}</p>
            
            <div class="mt-auto pt-4 border-t border-gray-100">
              <p class="text-2xl font-bold text-primary">{{ item.roi }}</p>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-12">
          <RouterLink to="/investment" class="btn btn-primary px-8 py-3 text-lg">
            查看投资方案
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 募资中短剧展示区 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资中短剧</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            精选优质短剧IP项目，打造爆款内容，共享行业红利
          </p>
        </div>
        
        <!-- 加载中状态 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
        </div>
        
        <!-- 错误提示 -->
        <div v-else-if="error" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <p class="mt-4 text-lg text-red-600">{{ error }}</p>
          <button 
            @click="loadDramaData" 
            class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            重试
          </button>
        </div>
        
        <!-- 无数据提示 -->
        <div v-else-if="dramas.length === 0" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p class="mt-4 text-lg text-gray-600">暂无募资中的短剧项目</p>
        </div>
        
        <!-- 响应式网格布局 - 限制只显示12个项目（2行x6列） -->
        <div v-else class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-x-4 gap-y-8">
          <!-- 项目卡片 -->
          <div
            v-for="project in displayedProjects"
            :key="project.id"
            class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative group border border-gray-100 hover:border-gray-200 cursor-pointer flex flex-col h-full"
            @click="navigateToProjectDetail(project.id)"
          >
            <!-- 封面图区域 (3:4比例) -->
            <div class="relative pt-[133.33%] bg-gray-100 overflow-hidden">
              <!-- 封面图 -->
              <div class="absolute inset-0 flex items-center justify-center transition-transform duration-500 group-hover:scale-105">
                <img
                  v-if="project.cover"
                  :src="project.cover"
                  :alt="project.title"
                  class="w-full h-full object-cover"
                />
                <div v-else class="w-full h-full flex items-center justify-center bg-purple-100">
                  <svg class="w-20 h-20 md:w-24 md:h-24 lg:w-20 lg:h-20 text-primary" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,17.27L18.18,21l-1.64-7.03L22,9.24l-7.19-0.61L12,2L9.19,8.63L2,9.24l5.46,4.73L5.82,21L12,17.27z" />
                  </svg>
                </div>
              </div>
              
              <!-- 剩余天数角标 -->
              <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-xs md:text-sm lg:text-xs font-bold rounded-md shadow-md">
                剩余 {{ project.remainingDays }} 天
              </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="p-3 md:p-4 lg:p-3 flex-1 flex flex-col">
              <!-- 标题 -->
              <h3 class="text-sm md:text-base lg:text-sm font-bold mb-2 h-6 md:h-7 lg:h-6 hover:text-primary transition-colors overflow-hidden">
                <div class="whitespace-nowrap animate-scroll-if-overflow">
                  {{ project.title }}
                </div>
              </h3>
              
              <!-- 类型标签 -->
              <div class="flex gap-1 mb-2 overflow-hidden">
                <DramaTag
                  v-for="tag in parseTagData(project.tags as any)"
                  :key="tag.id || tag.name"
                  :tag="tag"
                  class="text-[10px] md:text-xs lg:text-[10px] flex-shrink-0"
                />
              </div>
              
              <!-- 募资进度条 -->
              <div>
                <div class="flex justify-between text-xs md:text-sm lg:text-xs mb-1">
                  <span class="text-gray-600">已筹 {{ formatCurrency(project.currentFunding) }}</span>
                  <span class="font-medium">{{ calculateProgress(project.currentFunding, project.fundingGoal) }}%</span>
                </div>
                <div class="h-2 md:h-2.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full rounded-full bg-primary" 
                    :style="{
                      width: `${calculateProgress(project.currentFunding, project.fundingGoal)}%`
                    }"
                  ></div>
                </div>
                <div class="text-[10px] md:text-xs lg:text-[10px] text-gray-500 mt-1">
                  目标 {{ formatCurrency(project.fundingGoal) }}
                </div>
              </div>
            </div>
            
            <!-- 常驻按钮组 -->
            <div class="bg-white pt-2 px-3 pb-3 flex gap-2 mt-auto">
              <RouterLink
                :to="`/project/${project.id}`"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium text-xs transition-colors"
                @click.stop
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                查看详情
              </RouterLink>
              <button
                @click="handleInvestment(project)"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-primary hover:bg-primary-dark text-white text-center font-medium text-xs transition-colors"
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z" />
                </svg>
                参与众筹
              </button>
            </div>
          </div>
        </div>
        
        <!-- 查看更多按钮 - 链接到短剧筹募页面 -->
        <div class="text-center mt-12">
          <RouterLink to="/projects" class="btn btn-primary px-8 py-3 text-lg">
            查看更多短剧
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 募资流程时间轴 -->
    <section class="py-16 bg-gradient-to-r from-blue-50 to-purple-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资流程</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            为短剧制作方提供专业募资服务，从项目提交到资金到位，全程支持
          </p>
        </div>
        
        <!-- 桌面端横向时间轴 -->
        <div class="hidden md:block">
          <div class="relative">
            <!-- 流程步骤 -->
            <div class="grid grid-cols-4 gap-8 relative">
              <div 
                v-for="step in investmentProcess" 
                :key="step.id" 
                class="flex flex-col items-center text-center relative"
              >
                <!-- 序号圆圈 -->
                <div 
                  class="w-16 h-16 rounded-full bg-white border-4 border-primary flex items-center justify-center text-xl font-bold z-10 mb-6 relative"
                  @mouseenter="showTooltip(step.id)"
                  @mouseleave="hideTooltip()"
                >
                  {{ step.id }}
                  
                  <!-- 工具提示 -->
                  <div 
                    v-show="activeTooltip === step.id"
                    class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-white p-4 rounded-lg shadow-lg text-left z-20 text-sm"
                  >
                    <p class="text-gray-700">{{ step.details }}</p>
                    <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white rotate-45"></div>
                  </div>
                </div>
                
                <!-- 图标 -->
                <div class="bg-gradient-primary w-12 h-12 rounded-full flex items-center justify-center text-white mb-4">
                  <svg v-if="step.icon === 'search'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <svg v-else-if="step.icon === 'document-text'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <svg v-else-if="step.icon === 'film'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                  </svg>
                  <svg v-else-if="step.icon === 'cash'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                
                <!-- 标题和描述 -->
                <h3 class="text-lg font-bold mb-2">{{ step.title }}</h3>
                <p class="text-gray-600 text-sm">{{ step.description }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 移动端纵向时间轴 -->
        <div class="md:hidden">
          <div class="relative pl-10">
            <!-- 流程步骤 -->
            <div class="space-y-8">
              <div 
                v-for="step in investmentProcess" 
                :key="step.id" 
                class="relative"
              >
                <!-- 序号圆圈 -->
                <div 
                  class="absolute left-0 top-0 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white border-2 border-primary flex items-center justify-center text-lg font-bold z-10"
                  @click="activeTooltip === step.id ? hideTooltip() : showTooltip(step.id)"
                >
                  {{ step.id }}
                </div>
                
                <!-- 内容卡片 -->
                <div class="bg-white rounded-lg shadow-sm p-4">
                  <div class="flex items-center mb-3">
                    <!-- 图标 -->
                    <div class="bg-gradient-primary w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 flex-shrink-0">
                      <svg v-if="step.icon === 'search'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <svg v-else-if="step.icon === 'document-text'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <svg v-else-if="step.icon === 'film'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                      </svg>
                      <svg v-else-if="step.icon === 'cash'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    
                    <!-- 标题 -->
                    <h3 class="text-lg font-bold">{{ step.title }}</h3>
                  </div>
                  
                  <!-- 描述 -->
                  <p class="text-gray-600 text-sm">{{ step.description }}</p>
                  
                  <!-- 详情（点击后展开） -->
                  <div v-if="activeTooltip === step.id" class="mt-3 text-sm text-gray-700 bg-gray-50 p-3 rounded">
                    {{ step.details }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 募资注意事项 -->
        <div class="mt-16 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
              <svg class="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h4 class="text-lg font-semibold text-blue-800 mb-2">募资注意事项</h4>
              <ul class="text-sm text-blue-700 space-y-2">
                <li>• 项目募资门槛：单个项目最低募资金额10万元起</li>
                <li>• 制作周期要求：标准制作周期3-6个月，需按时完成项目交付</li>
                <li>• 收益分配机制：项目收益按投资比例分配，制作方享有创作收益和版权收益</li>
                <li>• 平台服务费：平台收取募资金额6-12%的服务费，用于项目推广和管理服务</li>
                <li>• 合规要求：项目内容需符合国家相关法规，通过平台内容审核</li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- 项目发布按钮 -->
        <div class="text-center mt-8">
          <button @click="showContactModal = true" class="btn bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-all">
            发布募资项目
          </button>
        </div>
      </div>
    </section>
    
    <!-- 募资人说 -->
    <section class="py-16 bg-gradient-to-br from-gray-50 to-white overflow-hidden">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资人说</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            听听那些成功募资的创作者们的真实声音
          </p>
        </div>

        <!-- 两行水平滚动布局 -->
        <div class="relative max-w-7xl mx-auto space-y-8">
          <!-- 第一行滚动 -->
          <div class="overflow-hidden">
            <div class="flex animate-scroll-horizontal-1 gap-6">
              <div
                v-for="review in getFirstRowReviews()"
                :key="`row1-${review.id}`"
                class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 backdrop-blur-sm flex-shrink-0 w-80"
              >
                <!-- 用户信息 -->
                <div class="flex items-center mb-4">
                  <!-- 头像显示首字母 -->
                  <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                    {{ review.name.charAt(0) }}
                  </div>
                  <div class="ml-3">
                    <h4 class="font-semibold text-gray-900 text-base">{{ review.name }}</h4>
                    <p class="text-gray-600 text-sm">{{ review.company }}</p>
                  </div>
                </div>

                <!-- 评价内容 -->
                <div class="text-gray-700 leading-relaxed text-sm">
                  <p class="italic">"{{ review.review }}"</p>
                </div>

                <!-- 星级评分 -->
                <div class="flex items-center mt-4 pt-4 border-t border-gray-100">
                  <div class="flex text-sm">
                    <i
                      v-for="(star, index) in getStarDisplay(review.rating)"
                      :key="index"
                      class="fas fa-star"
                      :class="{
                        'text-yellow-400': star === 'full' || star === 'half',
                        'text-gray-300': star === 'empty'
                      }"
                    ></i>
                  </div>
                  <span class="ml-2 text-xs text-gray-500">{{ review.rating }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二行滚动 -->
          <div class="overflow-hidden hidden md:block">
            <div class="flex animate-scroll-horizontal-2 gap-6">
              <div
                v-for="review in getSecondRowReviews()"
                :key="`row2-${review.id}`"
                class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 backdrop-blur-sm flex-shrink-0 w-80"
              >
                <!-- 用户信息 -->
                <div class="flex items-center mb-4">
                  <!-- 头像显示首字母 -->
                  <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                    {{ review.name.charAt(0) }}
                  </div>
                  <div class="ml-3">
                    <h4 class="font-semibold text-gray-900 text-base">{{ review.name }}</h4>
                    <p class="text-gray-600 text-sm">{{ review.company }}</p>
                  </div>
                </div>

                <!-- 评价内容 -->
                <div class="text-gray-700 leading-relaxed text-sm">
                  <p class="italic">"{{ review.review }}"</p>
                </div>

                <!-- 星级评分 -->
                <div class="flex items-center mt-4 pt-4 border-t border-gray-100">
                  <div class="flex text-sm">
                    <i
                      v-for="(star, index) in getStarDisplay(review.rating)"
                      :key="index"
                      class="fas fa-star"
                      :class="{
                        'text-yellow-400': star === 'full' || star === 'half',
                        'text-gray-300': star === 'empty'
                      }"
                    ></i>
                  </div>
                  <span class="ml-2 text-xs text-gray-500">{{ review.rating }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 合作伙伴Logo墙 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">合作伙伴</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            剧投投携手行业顶尖机构，共创短剧内容生态
          </p>
        </div>
        
        <!-- 短剧平台 -->
        <div class="mb-12">
          <!-- 加载状态 -->
          <div v-if="platformsLoading" class="text-center py-8">
            <div class="inline-flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-600">加载平台数据中...</span>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="platformsError" class="text-center py-8">
            <div class="text-red-500 mb-4">{{ platformsError }}</div>
            <button
              @click="loadPlatforms"
              class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              重新加载
            </button>
          </div>

          <!-- 平台列表 -->
          <div v-else-if="platforms.length > 0" class="logo-scroller overflow-hidden relative">
            <div class="scroller-inner flex animate-scroll items-center">
              <div
                v-for="platform in platforms"
                :key="`platform-${platform.id}`"
                class="logo-item flex-shrink-0 mx-1 transition-transform duration-300 hover:scale-110 cursor-pointer"
              >
                <div class="w-32 h-35 flex items-center justify-center rounded-xl p-2">
                  <img
                    v-if="platform.platform_logo_url"
                    :src="platform.platform_logo_url"
                    :alt="platform.platform_name"
                    class="max-w-full max-h-full object-contain rounded-lg"
                    @error="(e) => { (e.target as HTMLImageElement).style.display = 'none' }"
                  />
                  <span
                    class="text-sm font-bold text-gray-700"
                    :class="{ 'hidden': platform.platform_logo_url }"
                  >
                    {{ platform.platform_name }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 无数据状态 -->
          <div v-else class="text-center py-8">
            <div class="text-gray-500">暂无平台数据</div>
          </div>
        </div>
      </div>

    </section>
    
    <!-- 信任锚点横幅 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 z-40">
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 py-3 overflow-hidden">
        <div class="trust-scroll-container">
          <div class="trust-scroll-content">
            <!-- 信任标语 -->
            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <span class="text-sm text-gray-700">SSL 安全加密保护</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <span class="text-sm text-gray-700">资金银行托管</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <span class="text-sm text-gray-700">收益定期结算</span>
            </div>

            <!-- 统计数据 -->
            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm text-gray-700">累计募资 </span>
              <span class="text-sm font-bold text-primary ml-1">{{ websiteStats?.totalRaisedAmount?.toFixed(0) || '21690' }} 万元</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span class="text-sm text-gray-700">投资人数 </span>
              <span class="text-sm font-bold text-primary ml-1">{{ realtimeInvestors }} 位</span>
            </div>

            <!-- 真实投资记录 -->
            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">张** 刚刚投资 ¥100,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">李** 2分钟前投资 ¥200,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">王** 5分钟前投资 ¥150,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">陈** 8分钟前投资 ¥300,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">刘** 10分钟前投资 ¥250,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">赵** 15分钟前投资 ¥180,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">孙** 18分钟前投资 ¥120,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">周** 20分钟前投资 ¥350,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">吴** 25分钟前投资 ¥280,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">郑** 30分钟前投资 ¥160,000</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 为底部固定条留出空间 -->
    <div class="h-[112px]"></div>
    
    <!-- 行业快讯 News Ticker -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">行业快讯</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            实时掌握短剧行业动态，了解最新政策与市场趋势
          </p>
        </div>
        
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- 左侧垂直标签导航 -->
          <div class="w-full lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
              <div class="divide-y divide-gray-100">
                <button
                  v-for="(category, index) in newsCategories"
                  :key="index"
                  @click="changeNewsCategory(category)"
                  class="w-full py-4 px-6 text-left font-medium transition-colors relative flex items-center"
                  :class="activeNewsCategory === category ? 'text-primary bg-primary/5' : 'text-gray-700 hover:bg-gray-50'"
                >
                  <!-- 激活指示图标 -->
                  <svg v-if="activeNewsCategory === category" class="w-5 h-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                  <span>{{ category }}</span>
                  
                  <!-- 激活指示条 -->
                  <div 
                    v-if="activeNewsCategory === category" 
                    class="absolute left-0 top-0 bottom-0 w-1 bg-primary"
                  ></div>
                </button>
              </div>
            </div>
            
            <!-- 查看全部按钮 -->
            <div class="mt-4">
              <RouterLink to="/news" class="w-full py-3 px-6 bg-white rounded-xl shadow-sm border border-gray-200 flex justify-center items-center font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                <span>查看全部新闻</span>
                <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </RouterLink>
            </div>
          </div>
          
          <!-- 右侧新闻卡片网格 -->
          <div class="flex-grow">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 新闻卡片 -->
              <div
                v-for="news in filteredNews.slice(0, 6)"
                :key="news.id"
                class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 flex flex-col cursor-pointer"
                @click="navigateToNewsDetail(news.id)"
              >
                <!-- 封面图 (16:9比例) -->
                <div class="relative pt-[56.25%] bg-gray-200 overflow-hidden">
                  <!-- 封面图 -->
                  <img
                    :src="news.coverImage"
                    :alt="news.title"
                    class="absolute inset-0 w-full h-full object-cover"
                    @error="(e) => { (e.target as HTMLImageElement).style.display = 'none' }"
                  />

                  <!-- 封面图加载失败时的占位 -->
                  <div class="absolute inset-0 flex items-center justify-center bg-gray-200">
                    <svg class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v12a2 2 0 01-2 2z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 2v4M8 2v4M3 10h18" />
                    </svg>
                  </div>

                  <!-- 分类标签 -->
                  <div class="absolute top-2 left-2 px-2 py-1 bg-primary text-white text-xs font-bold rounded">
                    {{ news.category }}
                  </div>

                  <!-- New标签 (最近3天的新闻) -->
                  <div v-if="isRecentNews(news.publishDate)" class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                    New
                  </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="p-4 flex-grow flex flex-col">
                  <!-- 标题 -->
                  <h3 class="font-bold text-gray-800 mb-2 line-clamp-2 hover:text-primary hover:underline transition-colors">
                    {{ news.title }}
                  </h3>

                  <!-- 摘要 -->
                  <p class="text-sm text-gray-600 mb-4 line-clamp-2 flex-grow">
                    {{ news.summary }}
                  </p>

                  <!-- 底部信息 -->
                  <div class="flex justify-between items-center mt-auto pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500">{{ news.publishDate }}</span>
                      <span class="mx-2 text-gray-300">|</span>
                      <span class="text-xs text-gray-500">{{ news.author }}</span>
                      <span class="mx-2 text-gray-300">|</span>
                      <span class="text-xs text-gray-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {{ news.viewCount.toLocaleString() }}
                      </span>
                    </div>
                    
                    <!-- 分享按钮 (悬浮显示) -->
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                      <div class="flex space-x-2">
                        <button class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
                          <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.05 7.54a4.47 4.47 0 0 0-3.3-1.46 4.53 4.53 0 0 0-4.53 4.53c0 .35.04.7.08 1.05A12.9 12.9 0 0 1 5 6.89a4.3 4.3 0 0 0-.65 2.26c0 1.53.8 2.87 2 3.64a4.3 4.3 0 0 1-2.02-.57v.08a4.55 4.55 0 0 0 3.63 4.44c-.4.08-.8.13-1.21.13-.3 0-.56-.03-.87-.09a4.54 4.54 0 0 0 4.22 3.15 9.56 9.56 0 0 1-5.66 1.94c-.34 0-.7-.03-1.05-.08a13.36 13.36 0 0 0 7.04 2.04c8.08 0 12.52-6.7 12.52-12.52 0-.19-.01-.37-.01-.56a8.93 8.93 0 0 0 2.2-2.27c-.82.38-1.69.62-2.6.72a4.37 4.37 0 0 0 1.97-2.51z"></path>
                          </svg>
                        </button>
                        <button class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
                          <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21.385 15.992a1.599 1.599 0 0 1-1.695.054 8.217 8.217 0 0 0-4.948-1.158 1.574 1.574 0 0 1-1.673-1.472A1.574 1.574 0 0 1 14.54 11.72a11.321 11.321 0 0 1 6.814 1.593 1.575 1.575 0 0 1 .031 2.68zm.358-4.997a.999.999 0 0 1-1.039.037 12.133 12.133 0 0 0-7.128-1.593 1 1 0 0 1-1.066-.932.998.998 0 0 1 .933-1.066 14.133 14.133 0 0 1 8.298 1.855 1 1 0 0 1 .002 1.699zm-9.53-7.562a15 15 0 0 0-9.5 1.727A1.001 1.001 0 0 0 2.34 6.67c.36 0 .72-.13.996-.379A13 13 0 0 1 12 4.557a13 13 0 0 1 8.663 1.734.994.994 0 0 0 1.38-.28 1 1 0 0 0-.279-1.38 15 15 0 0 0-9.5-1.727zm-3.846 9.707a2.999 2.999 0 1 0 5.998 0 2.999 2.999 0 0 0-5.998 0z"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </div>

  <!-- 项目专管员联系弹窗 -->
  <div v-if="showContactModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showContactModal = false">
    <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4" @click.stop>
      <div class="text-center">
        <h3 class="text-xl font-bold mb-4">联系项目专管员</h3>
        <p class="text-gray-600 mb-6">扫描下方二维码，添加项目专管员微信，获取专业的募资服务支持</p>

        <!-- 二维码区域 -->
        <div class="bg-gray-100 w-48 h-48 mx-auto mb-6 flex items-center justify-center rounded-lg">
          <div class="text-center">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <p class="text-sm text-gray-500">项目专管员二维码</p>
            <p class="text-xs text-gray-400 mt-1">微信号：juTouTou2024</p>
          </div>
        </div>

        <div class="text-sm text-gray-500 mb-6">
          <p>工作时间：周一至周五 9:00-18:00</p>
          <p>专业服务：项目评估、募资指导、资金管理</p>
        </div>

        <button @click="showContactModal = false" class="w-full bg-primary text-white py-3 rounded-lg hover:bg-primary-dark transition-colors">
          我知道了
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-text-gradient {
  background: linear-gradient(to right, var(--color-purple-gradient-start), var(--color-purple-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent; /* 使用标准color属性替代非标准的text-fill-color */
}

/* 环形进度条动画 */
@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

svg path:nth-child(2) {
  animation: progress 1.5s ease-out forwards;
}

/* Logo墙滚动动画 */
.hover\:scale-120:hover {
  transform: scale(1.2);
}

.logo-scroller {
  mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
  display: flex;
  justify-content: center;
  align-items: center;
}

.scroller-inner {
  width: max-content;
  padding-block: 1rem;
  display: flex;
  align-items: center;
}

@keyframes scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-50% - 0.75rem));
  }
}

@keyframes scroll-reverse {
  from {
    transform: translateX(calc(-50% - 0.75rem));
  }
  to {
    transform: translateX(0);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll-reverse {
  animation: scroll-reverse 30s linear infinite;
}

/* 暂停滚动 */
.logo-scroller:hover .scroller-inner {
  animation-play-state: paused;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .fundraiser-review-card {
    width: 320px;
  }
}

/* 弹幕动画 */
.danmu-container {
  width: 100%;
  overflow: hidden;
}

.danmu-scroller {
  display: inline-block;
  white-space: nowrap;
  animation: danmu-scroll 30s linear infinite;
  padding: 2px 0;
}

@keyframes danmu-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 移动端信任信息滚动 */
.trust-info-scroller {
  display: inline-block;
  white-space: nowrap;
  animation: trust-info-scroll 15s linear infinite;
  padding: 2px 0;
}

@keyframes trust-info-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-120%);
  }
}

/* 标题滚动动画 */
.animate-scroll-if-overflow {
  display: inline-block;
  animation: scroll-text 8s linear infinite;
  animation-play-state: paused;
}

/* 当文本溢出时启用滚动动画 */
h3:hover .animate-scroll-if-overflow {
  animation-play-state: running;
}

@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(calc(-100% + 100px));
  }
  100% {
    transform: translateX(calc(-100% + 100px));
  }
}

/* 信任锚点滚动 */
.trust-scroll-container {
  overflow: hidden;
  width: 100%;
}

.trust-scroll-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
  animation: trust-scroll 30s linear infinite;
}

@keyframes trust-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 悬停暂停滚动 */
.trust-scroll-container:hover .trust-scroll-content {
  animation-play-state: paused;
}

/* 募资人说两行水平滚动动画 */
.animate-scroll-horizontal-1 {
  animation: scroll-horizontal-1 10s linear infinite;
}

.animate-scroll-horizontal-2 {
  animation: scroll-horizontal-2 7.5s linear infinite;
}

@keyframes scroll-horizontal-1 {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-horizontal-2 {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 悬停暂停动画 */
.animate-scroll-horizontal-1:hover,
.animate-scroll-horizontal-2:hover {
  animation-play-state: paused;
}

/* 自定义图标高度 */
.h-35 {
  height: 8.75rem; /* 35 * 0.25rem = 8.75rem */
}

/* 为底部栏留出空间 */
#app {
  padding-bottom: 112px;
}
</style>