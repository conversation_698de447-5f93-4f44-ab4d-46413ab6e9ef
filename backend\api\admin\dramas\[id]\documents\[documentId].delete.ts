/**
 * 删除短剧文档接口
 * DELETE /api/admin/dramas/[id]/documents/[documentId]
 */
export default defineEventHandler(async (event) => {
  try {
    // 验证管理员权限
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 获取参数
    const dramaId = getRouterParam(event, 'id');
    const documentId = getRouterParam(event, 'documentId');
    
    if (!dramaId || isNaN(Number(dramaId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的短剧ID'
      });
    }

    if (!documentId || isNaN(Number(documentId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的文档ID'
      });
    }

    // 检查文档是否存在且属于指定短剧
    const existingDocument = await query(
      'SELECT id, drama_id, name, file_url FROM drama_documents WHERE id = ? AND drama_id = ?',
      [documentId, dramaId]
    );

    if (existingDocument.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '短剧文档不存在'
      });
    }

    // 删除短剧文档
    await query(
      'DELETE FROM drama_documents WHERE id = ? AND drama_id = ?',
      [documentId, dramaId]
    );

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_DELETE_DRAMA_DOCUMENT',
      description: `管理员删除短剧文档: 短剧ID=${dramaId}, 文档ID=${documentId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      metadata: {
        dramaId: Number(dramaId),
        documentId: Number(documentId),
        deletedName: existingDocument[0].name,
        deletedFileUrl: existingDocument[0].file_url
      }
    });

    logger.info('短剧文档删除成功', {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      dramaId: Number(dramaId),
      documentId: Number(documentId),
      deletedName: existingDocument[0].name
    });

    return {
      success: true,
      message: '短剧文档删除成功',
      data: {
        id: Number(documentId),
        dramaId: Number(dramaId)
      }
    };

  } catch (error: any) {
    logger.error('删除短剧文档失败', {
      error: error.message,
      dramaId: getRouterParam(event, 'id'),
      documentId: getRouterParam(event, 'documentId')
    });

    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || '删除短剧文档失败'
    });
  }
});
