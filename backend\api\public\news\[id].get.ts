import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';

/**
 * 获取公开新闻详情接口
 * GET /api/public/news/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取新闻ID
    const newsId = getRouterParam(event, 'id');
    
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    // 查询新闻详情
    const newsQuery = `
      SELECT 
        n.id,
        n.title,
        n.summary,
        n.content,
        n.cover_image_url,
        n.author,
        n.source_url,
        n.view_count,
        n.is_featured,
        n.publish_date,
        n.created_at,
        n.updated_at,
        nc.id as category_id,
        nc.name as category_name,
        nc.slug as category_slug,
        nc.description as category_description
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      WHERE n.id = ? 
        AND n.status = 'published'
        AND n.publish_date <= NOW()
    `;

    const newsResult = await query(newsQuery, [parseInt(newsId)]);

    if (!newsResult || newsResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '新闻不存在或已下线'
      });
    }

    const news = newsResult[0];

    // 查询新闻标签
    const tagsQuery = `
      SELECT nt.id, nt.name
      FROM news_tags nt
      INNER JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      WHERE ntr.news_id = ?
      ORDER BY nt.name
    `;

    const tags = await query(tagsQuery, [parseInt(newsId)]);

    // 查询相关新闻（同分类的其他新闻）
    const relatedQuery = `
      SELECT 
        n.id,
        n.title,
        n.summary,
        n.cover_image_url,
        n.author,
        n.view_count,
        n.publish_date
      FROM news n
      WHERE n.category_id = ? 
        AND n.id != ?
        AND n.status = 'published'
        AND n.publish_date <= NOW()
      ORDER BY n.publish_date DESC
      LIMIT 5
    `;

    const relatedNews = news.category_id ? 
      await query(relatedQuery, [news.category_id, parseInt(newsId)]) : [];

    // 查询SEO信息
    const seoQuery = `
      SELECT 
        meta_title,
        meta_description,
        meta_keywords,
        canonical_url,
        og_title,
        og_description,
        og_image
      FROM news_seo
      WHERE news_id = ?
    `;

    const seoResult = await query(seoQuery, [parseInt(newsId)]);
    const seoInfo = seoResult.length > 0 ? seoResult[0] : null;

    // 格式化新闻详情数据
    const formattedNews = {
      id: news.id,
      title: news.title,
      summary: news.summary,
      content: news.content,
      coverImage: news.cover_image_url,
      author: news.author,
      sourceUrl: news.source_url,
      viewCount: news.view_count,
      isFeatured: news.is_featured === 1,
      publishDate: news.publish_date,
      createdAt: news.created_at,
      updatedAt: news.updated_at,
      category: news.category_id ? {
        id: news.category_id,
        name: news.category_name,
        slug: news.category_slug,
        description: news.category_description
      } : null,
      tags: tags.map((tag: any) => ({
        id: tag.id,
        name: tag.name
      })),
      relatedNews: relatedNews.map((related: any) => ({
        id: related.id,
        title: related.title,
        summary: related.summary,
        coverImage: related.cover_image_url,
        author: related.author,
        viewCount: related.view_count,
        publishDate: related.publish_date
      })),
      seo: seoInfo ? {
        metaTitle: seoInfo.meta_title,
        metaDescription: seoInfo.meta_description,
        metaKeywords: seoInfo.meta_keywords,
        canonicalUrl: seoInfo.canonical_url,
        ogTitle: seoInfo.og_title,
        ogDescription: seoInfo.og_description,
        ogImage: seoInfo.og_image
      } : null
    };

    return {
      success: true,
      data: formattedNews
    };

  } catch (error: any) {
    logger.error('获取新闻详情失败', {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, 'id'),
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取新闻详情失败'
    });
  }
});
