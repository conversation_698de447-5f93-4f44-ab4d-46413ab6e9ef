<script setup>
import { ref } from 'vue'

// 团队成员数据
const teamMembers = ref([
  {
    id: 1,
    name: '张明',
    title: '创始人 & CEO',
    bgColor: 'bg-blue-400',
    textColor: 'text-blue-800',
    background: '前爱奇艺内容总监，10年短视频内容运营经验',
    achievements: ['带领团队打造5部超10亿播放量短剧', '创立两家内容制作公司', '中国传媒大学硕士'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 2,
    name: '王丽',
    title: '艺术总监',
    bgColor: 'bg-purple-400',
    textColor: 'text-purple-800',
    background: '前优酷内容策划，8年影视剧创作经验',
    achievements: ['获得金鹰奖最佳编剧提名', '策划及创作15部热门网络剧', '北京电影学院导演系'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 3,
    name: '李强',
    title: '投资总监',
    bgColor: 'bg-green-400',
    textColor: 'text-green-800',
    background: '前华映资本投资经理，专注文娱产业投资',
    achievements: ['主导投资20+文娱项目，IRR超40%', '成功退出6个项目', '清华大学金融学硕士'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 4,
    name: '赵芳',
    title: '市场营销总监',
    bgColor: 'bg-yellow-400',
    textColor: 'text-yellow-800',
    background: '前字节跳动营销负责人，精通短视频营销',
    achievements: ['打造3个破亿观看量的营销活动', '建立高效内容推广体系', '复旦大学市场营销学士'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 5,
    name: '陈宇',
    title: '技术总监',
    bgColor: 'bg-red-400',
    textColor: 'text-red-800',
    background: '前腾讯高级工程师，专注视频技术与数据分析',
    achievements: ['研发短视频内容推荐算法', '优化播放体验提升30%用户留存', '上海交大计算机科学博士'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 6,
    name: '刘敏',
    title: '内容制作总监',
    bgColor: 'bg-indigo-400',
    textColor: 'text-indigo-800',
    background: '资深制片人，参与制作多部爆款网剧',
    achievements: ['监制8部单集播放量破千万的短剧', '管理50+人创作团队', '中央戏剧学院表演系'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 7,
    name: '吴杰',
    title: '财务总监',
    bgColor: 'bg-pink-400',
    textColor: 'text-pink-800',
    background: '前德勤高级审计师，专注娱乐行业财务规划',
    achievements: ['搭建完善财务合规体系', '优化资金利用率提升25%', '上海财经大学会计硕士'],
    linkedin: '#',
    portfolio: '#'
  },
  {
    id: 8,
    name: '黄嘉',
    title: '法务总监',
    bgColor: 'bg-cyan-400',
    textColor: 'text-cyan-800',
    background: '前金杜律师事务所律师，专注知识产权保护',
    achievements: ['处理超过100起版权纠纷案件', '建立IP保护体系', '北京大学法学博士'],
    linkedin: '#',
    portfolio: '#'
  }
])

// 顾问团队数据
const advisors = ref([
  {
    id: 1,
    name: '杨教授',
    title: '内容策略顾问',
    bgColor: 'bg-amber-400',
    textColor: 'text-amber-800',
    background: '中国传媒大学新媒体研究中心主任',
    achievements: ['发表30+媒体研究论文', '出版《短视频内容创作》等专著', '国家广电总局专家委员会成员'],
    linkedin: '#'
  },
  {
    id: 2,
    name: '周总',
    title: '投资战略顾问',
    bgColor: 'bg-emerald-400',
    textColor: 'text-emerald-800',
    background: '某知名投资基金合伙人，专注文创产业',
    achievements: ['主导百亿级文化产业基金', '投资20+独角兽企业', '前华为战略投资部总监'],
    linkedin: '#'
  },
  {
    id: 3,
    name: '林博士',
    title: '技术创新顾问',
    bgColor: 'bg-violet-400',
    textColor: 'text-violet-800',
    background: 'AI视觉技术专家，曾任谷歌研究员',
    achievements: ['10+计算机视觉专利', '开发新一代视频压缩算法', 'IEEE高级会员'],
    linkedin: '#'
  }
])

// 当前查看的成员详情ID
const activeDetailId = ref(null)

// 查看详情
const showDetail = (id) => {
  activeDetailId.value = activeDetailId.value === id ? null : id
}
</script>

<template>
  <div>
    <!-- 团队介绍头部 -->
    <section class="bg-gradient-primary text-white py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">行业精英团队</h1>
          <p class="text-xl opacity-90 leading-relaxed">
            剧投投汇聚了来自内容创作、市场运营、投资管理等领域的顶尖人才，
            我们的团队拥有丰富的行业经验和成功案例，致力于打造优质、高回报的短剧内容。
          </p>
        </div>
      </div>
    </section>
    
    <!-- 核心团队 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">核心团队</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            我们的核心成员来自行业头部公司，拥有丰富的项目经验和执行能力
          </p>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
          <!-- 团队成员卡片 -->
          <div 
            v-for="member in teamMembers" 
            :key="member.id"
            class="card overflow-hidden transition-all duration-300 hover:shadow-lg"
            :class="{'ring-2 ring-primary': activeDetailId === member.id}"
          >
            <!-- 成员头像 - 使用彩色背景和姓名首字母替代图片 -->
            <div class="relative h-64">
              <div 
                :class="['w-full h-full flex items-center justify-center', member.bgColor]"
              >
                <span :class="['text-6xl font-bold', member.textColor]">{{ member.name.charAt(0) }}</span>
              </div>
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end">
                <div class="p-4 text-white">
                  <div class="flex space-x-3">
                    <a :href="member.linkedin" class="bg-primary hover:bg-primary-dark p-2 rounded-full">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                    </a>
                    <a :href="member.portfolio" class="bg-gray-700 hover:bg-gray-600 p-2 rounded-full">
                      <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 成员信息 -->
            <div class="p-4">
              <h3 class="font-bold text-lg">{{ member.name }}</h3>
              <p class="text-primary">{{ member.title }}</p>
              <p class="text-sm text-gray-600 mt-2 line-clamp-2">{{ member.background }}</p>
              
              <!-- 查看详情按钮 -->
              <button 
                @click="showDetail(member.id)" 
                class="mt-3 text-sm text-primary hover:text-primary-dark font-medium flex items-center"
              >
                {{ activeDetailId === member.id ? '收起详情' : '查看详情' }}
                <svg 
                  class="w-4 h-4 ml-1 transition-transform"
                  :class="activeDetailId === member.id ? 'rotate-180' : ''" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </button>
            </div>
            
            <!-- 展开的详细信息 -->
            <div 
              v-if="activeDetailId === member.id" 
              class="p-4 bg-blue-50 border-t border-blue-100"
            >
              <h4 class="font-medium text-sm mb-2">主要成就:</h4>
              <ul class="text-sm text-gray-700 space-y-1 pl-4 list-disc">
                <li v-for="(achievement, i) in member.achievements" :key="i">
                  {{ achievement }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 顾问团队 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">专业顾问团</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            拥有业内权威专家组成的顾问团队，为项目提供专业指导与支持
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <!-- 顾问卡片 -->
          <div 
            v-for="advisor in advisors" 
            :key="advisor.id"
            class="card overflow-hidden hover:shadow-lg transition-all duration-300"
          >
            <div class="flex items-center p-4 border-b">
              <div 
                :class="['w-20 h-20 rounded-full flex items-center justify-center mr-4', advisor.bgColor]"
              >
                <span :class="['text-2xl font-bold', advisor.textColor]">{{ advisor.name.charAt(0) }}</span>
              </div>
              <div>
                <h3 class="font-bold text-lg">{{ advisor.name }}</h3>
                <p class="text-primary">{{ advisor.title }}</p>
              </div>
            </div>
            
            <div class="p-4">
              <p class="text-gray-700 mb-3">{{ advisor.background }}</p>
              <h4 class="font-medium text-sm mb-2">专长领域:</h4>
              <ul class="text-sm text-gray-700 space-y-1 pl-4 list-disc">
                <li v-for="(achievement, i) in advisor.achievements" :key="i">
                  {{ achievement }}
                </li>
              </ul>
              
              <div class="mt-4">
                <a :href="advisor.linkedin" class="flex items-center text-sm text-primary hover:text-primary-dark">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                  </svg>
                  专业主页
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 团队荣誉 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">团队荣誉</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            我们的创作与制作团队一直保持着优秀的业界口碑
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto">
          <div class="flex flex-col space-y-8">
            <div class="flex">
              <div class="flex-shrink-0 mr-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center text-primary">
                  <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-2">8年+短视频行业经验</h3>
                <p class="text-gray-600">
                  团队核心成员平均拥有8年以上短视频内容创作与运营经验，熟悉内容生态与传播规律。
                </p>
              </div>
            </div>
            
            <div class="flex">
              <div class="flex-shrink-0 mr-4">
                <div class="w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary">
                  <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-2">50+成功案例</h3>
                <p class="text-gray-600">
                  已成功打造50部以上的精品短剧内容，累计获得超过50亿次播放量与1000万粉丝。
                </p>
              </div>
            </div>
            
            <div class="flex">
              <div class="flex-shrink-0 mr-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center text-primary">
                  <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-2">98%投资人满意度</h3>
                <p class="text-gray-600">
                  我们重视每一位投资人的权益，确保高透明度的项目管理与沟通，获得极高的满意度评价。
                </p>
              </div>
            </div>
          </div>
          
          <div class="flex flex-col space-y-8">
            <div class="flex">
              <div class="flex-shrink-0 mr-4">
                <div class="w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary">
                  <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-2">多元化专业背景</h3>
                <p class="text-gray-600">
                  团队成员来自内容创作、营销推广、投资金融等多元背景，形成强大的互补能力。
                </p>
              </div>
            </div>
            
            <div class="flex">
              <div class="flex-shrink-0 mr-4">
                <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center text-primary">
                  <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-2">15+行业奖项</h3>
                <p class="text-gray-600">
                  团队作品获得国内外多个行业奖项与认可，包括金鹰奖提名、金鼎奖等专业荣誉。
                </p>
              </div>
            </div>
            
            <div class="flex">
              <div class="flex-shrink-0 mr-4">
                <div class="w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center text-secondary">
                  <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 class="font-bold text-lg mb-2">深厚行业资源</h3>
                <p class="text-gray-600">
                  拥有丰富的行业资源与合作网络，包括头部平台、知名演员、专业制作团队等。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 加入我们 -->
    <section class="py-16 bg-gradient-primary text-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">加入剧投投团队</h2>
          <p class="text-xl opacity-90 mb-8">
            我们始终在寻找优秀的人才加入我们的团队。如果您热爱创意内容制作，
            并希望在快速发展的短剧市场大展身手，剧投投将是您理想的选择。
          </p>
          <button class="btn bg-white text-primary hover:bg-blue-50 px-8 py-3 text-lg">
            查看招聘职位
          </button>
        </div>
      </div>
    </section>
  </div>
</template> 