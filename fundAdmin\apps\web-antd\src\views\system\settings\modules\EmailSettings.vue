<template>
  <div class="settings-container">
    <a-form
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      @finish="handleSubmit"
      class="settings-form"
      layout="horizontal"
    >
      <a-form-item
        label="SMTP服务器"
        name="host"
        :rules="[
          { required: true, message: '请输入SMTP服务器地址' },
          { pattern: /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入有效的服务器地址' }
        ]"
      >
        <a-input v-model:value="formState.host" placeholder="例如：smtp.qq.com" />
      </a-form-item>

      <a-form-item
        label="端口"
        name="port"
        :rules="[
          { required: true, message: '请输入端口号' },
          { type: 'number', min: 1, max: 65535, message: '端口号应在1-65535之间' }
        ]"
      >
        <a-input-number
          v-model:value="formState.port"
          :min="1"
          :max="65535"
          style="width: 100%"
          placeholder="常用端口：25, 465, 587"
        />
      </a-form-item>

      <a-form-item
        label="用户名"
        name="user"
        :rules="[
          { required: true, message: '请输入用户名' },
          { type: 'email', message: '请输入有效的邮箱地址' }
        ]"
      >
        <a-input v-model:value="formState.user" />
      </a-form-item>

      <a-form-item
        label="密码"
        name="password"
        :rules="[{ required: true, message: '请输入密码或授权码' }]"
      >
        <a-input v-model:value="formState.password" placeholder="请输入密码或授权码" />
      </a-form-item>

      <a-form-item
        label="发件人名称"
        name="from"
      >
        <a-input v-model:value="formState.from" placeholder="剧投投 <<EMAIL>>" />
      </a-form-item>

      <a-form-item name="secure" :wrapper-col="{ span: 18, offset: 6 }">
        <a-checkbox v-model:checked="formState.secure">
          使用SSL/TLS加密
        </a-checkbox>
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 20, offset: 4 }">
        <div class="flex justify-end space-x-4">
          <a-button @click="handleTest" :loading="testing">
            {{ testing ? '测试中...' : '测试邮件' }}
          </a-button>
          <a-button type="primary" html-type="submit" :loading="saving">
            {{ saving ? '保存中...' : '保存设置' }}
          </a-button>
        </div>
      </a-form-item>
    </a-form>

    <!-- 测试邮件对话框 -->
    <a-modal
      v-model:visible="testModalVisible"
      title="测试邮件"
      @ok="confirmTest"
      :okButtonProps="{ loading: testing }"
    >
      <a-form :model="testForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item
          label="收件人"
          name="to"
          :rules="[
            { required: true, message: '请输入收件人邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' }
          ]"
        >
          <a-input v-model:value="testForm.to" />
        </a-form-item>
        <a-form-item
          label="主题"
          name="subject"
          :rules="[{ required: true, message: '请输入邮件主题' }]"
        >
          <a-input v-model:value="testForm.subject" />
        </a-form-item>
        <a-form-item
          label="内容"
          name="text"
          :rules="[{ required: true, message: '请输入邮件内容' }]"
        >
          <a-textarea v-model:value="testForm.text" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import {
  Form as AForm,
  FormItem as AFormItem,
  Input as AInput,
  InputNumber as AInputNumber,
  Textarea as ATextarea,
  Button as AButton,
  Checkbox as ACheckbox,
  Modal as AModal,
  message
} from 'ant-design-vue';
import { getEmailSettings, updateEmailSettings, testEmailSettings } from '#/api/system/settings';
import type { EmailSettings, TestEmailParams } from '#/api/system/settings/types';

// 表单状态
const formState = reactive<EmailSettings>({
  host: '',
  port: 465,
  secure: true,
  user: '',
  password: '',
  from: '',
});

// 测试表单
const testForm = reactive<TestEmailParams>({
  to: '',
  subject: '测试邮件',
  text: '这是一封测试邮件，用于验证邮件设置是否正确。',
});

// 状态
const saving = ref(false);
const testing = ref(false);
const testModalVisible = ref(false);

// 加载邮件设置
const loadSettings = async () => {
  try {
    console.log('开始加载邮件设置...');
    const data = await getEmailSettings();
    console.log('邮件设置API响应:', data);

    if (data) {
      console.log('邮件设置数据:', data);
      console.log('密码字段值:', data.password);
      Object.assign(formState, data);
      console.log('表单状态更新后:', formState);
      console.log('表单密码字段:', formState.password);
    } else {
      console.warn('邮件设置响应中没有数据');
    }
  } catch (error: any) {
    console.error('加载邮件设置失败:', error);
    message.error(error.message || '加载邮件设置失败');
  }
};

// 提交表单
const handleSubmit = async () => {
  saving.value = true;
  try {
    console.log('提交邮件设置:', formState);
    await updateEmailSettings(formState);
    message.success('邮件设置保存成功');
    // 重新加载数据以确保显示最新的设置
    await loadSettings();
  } catch (error: any) {
    console.error('保存邮件设置失败:', error);
    message.error(error.message || '保存邮件设置失败');
  } finally {
    saving.value = false;
  }
};

// 打开测试对话框
const handleTest = () => {
  // 预填充测试表单
  testForm.to = formState.user;
  testModalVisible.value = true;
};

// 确认测试
const confirmTest = async () => {
  testing.value = true;
  try {
    await testEmailSettings(testForm);
    message.success('测试成功');
    testModalVisible.value = false;
  } catch (error: any) {
    message.error(error.message || '测试失败');
  } finally {
    testing.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadSettings();
});
</script>

<style scoped>
@import '../styles/common.css';
</style>
