/**
 * 获取前端配置信息接口
 * GET /api/public/config
 */
export default defineEventHandler(async (event) => {
  try {
    // 并行获取各种配置数据
    const [settingsResult, bannersResult] = await Promise.all([
      // 获取系统设置
      query(
        'SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN (?)',
        [['site_settings', 'investment_settings']]
      ),
      
      // 获取激活的横幅
      query(
        `SELECT id, title, subtitle, image_url, link_url, background_color, 
                text_color, open_in_new_tab, sort_order
         FROM banners 
         WHERE is_active = 1 
         ORDER BY sort_order ASC, id ASC`
      )
    ]);

    // 处理设置数据
    const settings: Record<string, any> = {};
    if (settingsResult && settingsResult.length > 0) {
      settingsResult.forEach((row: any) => {
        try {
          settings[row.setting_key] = JSON.parse(row.setting_value);
        } catch (err) {
          logger.warn(`解析设置JSON失败: ${row.setting_key}`, { error: err.message });
          settings[row.setting_key] = {};
        }
      });
    }

    // 设置默认值
    if (!settings.site_settings) {
      settings.site_settings = {
        title: '剧投投',
        description: '中国最专业的短剧投资平台',
        logo: '/images/logo.png',
        favicon: '/favicon.ico',
        icp: '粤ICP备XXXXXXXX号',
        copyright: '© 2023 剧投投. 保留所有权利'
      };
    }

    if (!settings.investment_settings) {
      settings.investment_settings = {
        minAmount: 10000,
        maxAmount: 1000000,
        minReturnRate: 8,
        platformFee: 2
      };
    }

    // 格式化横幅数据
    const banners = bannersResult.map((banner: any) => ({
      id: banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      imageUrl: banner.image_url,
      linkUrl: banner.link_url,
      backgroundColor: banner.background_color,
      textColor: banner.text_color,
      openInNewTab: banner.open_in_new_tab === 1,
      sortOrder: banner.sort_order
    }));

    // 构建完整的配置响应
    const config = {
      // 网站基本设置
      site: settings.site_settings,
      
      // 投资相关设置
      investment: settings.investment_settings,
      
      // 横幅数据
      banners,
      
      // 系统信息
      system: {
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      },
      
      // API配置
      api: {
        version: 'v1',
        baseUrl: '/api'
      }
    };

    return {
      success: true,
      data: config
    };

  } catch (error: any) {
    logger.error('获取前端配置失败', {
      error: error.message,
      ip: getHeader(event, 'x-forwarded-for') || getHeader(event, 'x-real-ip') || 'unknown'
    });

    // 返回默认配置，确保前端正常工作
    return {
      success: true,
      data: {
        site: {
          title: '剧投投',
          description: '中国最专业的短剧投资平台',
          logo: '/images/logo.png',
          favicon: '/favicon.ico',
          icp: '粤ICP备XXXXXXXX号',
          copyright: '© 2023 剧投投. 保留所有权利'
        },
        investment: {
          minAmount: 10000,
          maxAmount: 1000000,
          minReturnRate: 8,
          platformFee: 2
        },
        banners: [],
        system: {
          version: '1.0.0',
          environment: 'production',
          timestamp: new Date().toISOString()
        },
        api: {
          version: 'v1',
          baseUrl: '/api'
        }
      }
    };
  }
});
