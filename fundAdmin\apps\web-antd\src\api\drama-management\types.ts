// 短剧管理相关类型定义

export namespace DramaManagementApi {
  // 短剧状态枚举
  export type DramaStatus = 'draft' | 'published' | 'ended' | 'archived';

  // 素材类型枚举
  export type MaterialType = 'image' | 'video';

  // 投资层级
  export interface InvestmentTier {
    minAmount: number;
    benefits: string[];
  }

  // 制作团队信息
  export interface ProductionTeam {
    productionCompany?: string;
    coProductionCompany?: string;
    executiveProducer?: string;
    coExecutiveProducer?: string;
    chiefProducer?: string;
    producer?: string;
    coProducer?: string;
    director?: string;
    scriptwriter?: string;
    supervisor?: string;
    coordinator?: string;
  }

  // 制作进度信息
  export interface ProductionSchedule {
    preProduction?: string;
    filming?: string;
    postProduction?: string;
    expectedReleaseDate?: string;
  }

  // 募资信息
  export interface FundingInfo {
    fundingGoal?: number;
    currentFunding?: number;
    fundingEndDate?: string;
    fundingShare?: number;
    minInvestment?: number;
    roi?: number;
    status?: DramaStatus;
  }

  // 剧本文档信息
  export interface DramaDocument {
    id?: number;
    dramaId?: number;
    name: string;
    fileUrl: string;
    fileType?: string;
    fileSize?: string;
    createdAt?: string;
    updatedAt?: string;
  }

  // 其他信息
  export interface AdditionalInfo {
    riskManagement?: string[];
    confirmedResources?: string[];
    investmentTiers?: InvestmentTier[];
  }

  // 短剧基本信息
  export interface Drama {
    id: number;
    title: string;
    cover: string;
    tags: string[];
    description: string;
    episodes: number;
    episodeLength: number;
    targetPlatform: string[];
    projectedViews?: string;
    cast: string[];
    isOnline: number;
    creatorId?: number;
    createdAt: string;
    updatedAt: string;
    // 关联信息
    productionTeam?: ProductionTeam;
    productionSchedule?: ProductionSchedule;
    fundingInfo?: FundingInfo;
    additionalInfo?: AdditionalInfo;
  }

  // 短剧素材
  export interface DramaMaterial {
    id: number;
    dramaId: number;
    type: MaterialType;
    title?: string;
    url: string;
    thumbnail?: string;
    sortOrder: number;
    createdAt: string;
    updatedAt: string;
  }

  // 短剧详情（包含关联数据）
  export interface DramaDetail extends Drama {
    materials: DramaMaterial[];
    documents: DramaDocument[];
  }

  // 分页信息
  export interface Pagination {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  }

  // 短剧列表查询参数
  export interface DramaListParams {
    page?: number;
    pageSize?: number;
    search?: string;
    status?: DramaStatus;
  }

  // 短剧列表响应
  export interface DramaListResponse {
    list: Drama[];
    pagination: Pagination;
  }

  // 短剧详情响应
  export interface DramaDetailResponse extends DramaDetail {}

  // 创建短剧请求参数
  export interface CreateDramaParams {
    // 基础信息
    title: string;
    cover: string;
    tags?: string[];
    description: string;
    episodes: number;
    episodeLength: number;
    targetPlatform?: string[];
    projectedViews?: string;
    cast?: string[];
    isOnline?: number;
    // 制作团队信息
    productionTeam?: ProductionTeam;
    // 制作进度信息
    productionSchedule?: ProductionSchedule;
    // 募资信息
    fundingInfo?: FundingInfo;
    // 其他信息
    additionalInfo?: AdditionalInfo;
  }

  // 更新短剧请求参数
  export interface UpdateDramaParams {
    // 基础信息
    title?: string;
    cover?: string;
    tags?: string[];
    description?: string;
    episodes?: number;
    episodeLength?: number;
    targetPlatform?: string[];
    projectedViews?: string;
    cast?: string[];
    isOnline?: number;
    // 制作团队信息
    productionTeam?: ProductionTeam;
    // 制作进度信息
    productionSchedule?: ProductionSchedule;
    // 募资信息
    fundingInfo?: FundingInfo;
    // 其他信息
    additionalInfo?: AdditionalInfo;
  }

  // 操作响应
  export interface OperationResponse {
    success: boolean;
    message: string;
    data?: any;
  }

  // 素材管理相关
  export interface CreateMaterialParams {
    dramaId: number;
    type: MaterialType;
    title?: string;
    url: string;
    thumbnail?: string;
    sortOrder?: number;
  }

  export interface UpdateMaterialParams extends Partial<CreateMaterialParams> {
    id: number;
  }
}

/** 短剧创建/更新表单数据 */
export interface DramaFormData {
  /** 短剧标题 */
  title: string;
  /** 封面图片URL */
  cover: string;
  /** 标签列表 */
  tags: string[];
  /** 短剧描述 */
  description: string;
  /** 集数 */
  episodes: number;
  /** 单集时长（分钟） */
  episodeLength: number;
  /** 目标平台 */
  targetPlatform: string[];
  /** 预计播放量 */
  projectedViews?: string;
  /** 演员列表 */
  cast: string[];
  /** 是否上线 */
  isOnline: number;
  /** 制作团队信息 */
  productionTeam?: DramaManagementApi.ProductionTeam;
  /** 制作进度信息 */
  productionSchedule?: DramaManagementApi.ProductionSchedule;
  /** 募资信息 */
  fundingInfo?: DramaManagementApi.FundingInfo;
  /** 其他信息 */
  additionalInfo?: DramaManagementApi.AdditionalInfo;
}

/** 短剧列表查询参数 */
export interface DramaListParams {
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 状态筛选 */
  status?: DramaStatus;
  /** 搜索关键词 */
  search?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

/** 短剧列表响应数据 */
export interface DramaListResponse {
  /** 短剧列表 */
  list: DramaManagementApi.Drama[];
  /** 分页信息 */
  pagination: {
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    pageSize: number;
    /** 总数量 */
    total: number;
    /** 总页数 */
    totalPages: number;
  };
}

/** 素材创建/更新表单数据 */
export interface MaterialFormData {
  /** 关联的短剧ID */
  dramaId: number;
  /** 素材标题 */
  title?: string;
  /** 素材URL */
  url: string;
  /** 缩略图URL（视频素材使用） */
  thumbnail?: string;
  /** 素材类型 */
  type: MaterialType;
  /** 排序顺序 */
  sortOrder: number;
}

/** 素材列表查询参数 */
export interface MaterialListParams {
  /** 短剧ID */
  dramaId: number;
  /** 素材类型筛选 */
  type?: MaterialType;
}

// 短剧状态选项
export const DRAMA_STATUS_OPTIONS = [
  { label: '草稿', value: 'draft' },
  { label: '已发布', value: 'published' },
  { label: '已结束', value: 'ended' },
  { label: '已归档', value: 'archived' },
] as const;

// 素材类型选项
export const MATERIAL_TYPE_OPTIONS = [
  { label: '图片', value: 'image' },
  { label: '视频', value: 'video' },
] as const;

// 目标平台选项
export const TARGET_PLATFORM_OPTIONS = [
  { label: '红果短剧', value: '红果短剧' },
  { label: '剧投投', value: '剧投投' },
  { label: '剧投投', value: '剧投投' },
  { label: '快手', value: '快手' },
  { label: '抖音', value: '抖音' },
  { label: '微信视频号', value: '微信视频号' },
] as const;


