/**
 * 获取多个公开设置接口
 * GET /api/public/settings
 */
export default defineEventHandler(async (event) => {
  try {
    // 需要获取的设置键
    const settingKeys = [
      'site_settings',
      'investment_settings'
    ];
    
    // 查询设置
    const result = await query(
      'SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN (?)',
      [settingKeys]
    );
    
    // 处理结果
    const settings: Record<string, any> = {};
    if (result && result.length > 0) {
      result.forEach((row: any) => {
        try {
          settings[row.setting_key] = JSON.parse(row.setting_value);
        } catch (err) {
          logger.warn(`解析设置JSON失败: ${row.setting_key}`, { error: err.message });
          settings[row.setting_key] = {};
        }
      });
    }
    
    // 处理默认值
    if (!settings.site_settings) {
      settings.site_settings = {
        title: '剧投投',
        description: '中国最专业的短剧投资平台',
        logo: '/images/logo.png',
        favicon: '/favicon.ico',
        icp: '粤ICP备XXXXXXXX号',
        copyright: '© 2023 剧投投. 保留所有权利'
      };
    }
    
    if (!settings.investment_settings) {
      settings.investment_settings = {
        minAmount: 10000,
        maxAmount: 1000000,
        minReturnRate: 8,
        platformFee: 2
      };
    }
    
    return {
      success: true,
      data: settings
    };

  } catch (error: any) {
    logger.error('获取公开设置失败', { 
      error: error.message,
      ip: getClientIP(event)
    });
    
    // 返回默认设置，确保前端正常工作
    return {
      success: true,
      data: {
        site_settings: {
          title: '剧投投',
          description: '中国最专业的短剧投资平台',
          logo: '/images/logo.png',
          favicon: '/favicon.ico',
          icp: '粤ICP备XXXXXXXX号',
          copyright: '© 2023 剧投投. 保留所有权利'
        },
        investment_settings: {
          minAmount: 10000,
          maxAmount: 1000000,
          minReturnRate: 8,
          platformFee: 2
        }
      }
    };
  }
});
