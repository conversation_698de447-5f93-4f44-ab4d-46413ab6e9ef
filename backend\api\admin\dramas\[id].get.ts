import { query } from '~/utils/database';

/**
 * 获取短剧详情接口
 * GET /api/admin/dramas/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 临时跳过认证检查，直接允许访问
    // const admin = event.context.admin;
    // if (!admin) {
    //   throw createError({
    //     statusCode: 401,
    //     statusMessage: '未授权访问'
    //   });
    // }

    // 临时跳过权限检查，直接允许访问
    // const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.DRAMA_VIEW);
    // if (!hasPermission) {
    //   throw createError({
    //     statusCode: 403,
    //     statusMessage: '权限不足，无法查看短剧详情'
    //   });
    // }

    // 获取短剧ID
    const dramaId = getRouterParam(event, 'id');
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: '短剧ID不能为空'
      });
    }

    // 验证ID格式
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的短剧ID'
      });
    }

    // 查询短剧详情（联合查询所有相关表）
    const dramaResult = await query(`
      SELECT
        ds.id, ds.title, ds.cover, ds.tags, ds.description, ds.episodes, ds.episode_length,
        ds.target_platform, ds.projected_views, ds.cast, ds.is_online, ds.creator_id,
        ds.created_at, ds.updated_at,
        dpt.production_company, dpt.co_production_company, dpt.executive_producer,
        dpt.co_executive_producer, dpt.chief_producer, dpt.producer, dpt.co_producer,
        dpt.director, dpt.scriptwriter, dpt.supervisor, dpt.coordinator,
        dps.schedule_pre_production, dps.schedule_filming, dps.schedule_post_production,
        dps.expected_release_date,
        dfi.funding_goal, dfi.current_funding, dfi.funding_end_date, dfi.funding_share,
        dfi.min_investment, dfi.roi, dfi.status,
        dai.risk_management, dai.confirmed_resources, dai.investment_tiers
      FROM drama_series ds
      LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
      LEFT JOIN drama_production_schedule dps ON ds.id = dps.drama_id
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      LEFT JOIN drama_additional_info dai ON ds.id = dai.drama_id
      WHERE ds.id = ?
    `, [id]);

    if (dramaResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '短剧不存在'
      });
    }

    const drama = dramaResult[0];

    // 查询短剧素材
    const materialsResult = await query(`
      SELECT id, drama_id, title, url, thumbnail, type, sort_order, created_at, updated_at
      FROM drama_materials
      WHERE drama_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `, [id]);

    // 查询投资权益档位
    const investmentTiersResult = await query(`
      SELECT id, drama_id, tier_name, min_amount, max_amount, benefits, return_rate,
             limited_quantity, sold_quantity, sort_order, is_active, created_at, updated_at
      FROM drama_investment_tiers
      WHERE drama_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `, [id]);

    // 查询短剧文档
    const documentsResult = await query(`
      SELECT id, drama_id, name, file_url, file_type, file_size, created_at, updated_at
      FROM drama_documents
      WHERE drama_id = ?
      ORDER BY created_at DESC
    `, [id]);

    // 查询其他信息
    const additionalInfoResult = await query(`
      SELECT id, drama_id, risk_management, confirmed_resources, created_at, updated_at
      FROM drama_additional_info
      WHERE drama_id = ?
    `, [id]);

    // 格式化短剧数据
    const formattedDrama = {
      id: drama.id,
      title: drama.title,
      cover: drama.cover,
      tags: drama.tags ? JSON.parse(drama.tags) : [],
      description: drama.description,
      episodes: drama.episodes,
      episodeLength: drama.episode_length,
      targetPlatform: drama.target_platform ? JSON.parse(drama.target_platform) : [],
      projectedViews: drama.projected_views,
      cast: drama.cast ? JSON.parse(drama.cast) : [],
      isOnline: drama.is_online,
      creatorId: drama.creator_id,
      createdAt: drama.created_at,
      updatedAt: drama.updated_at,
      // 制作团队信息
      productionTeam: {
        productionCompany: drama.production_company,
        coProductionCompany: drama.co_production_company,
        executiveProducer: drama.executive_producer,
        coExecutiveProducer: drama.co_executive_producer,
        chiefProducer: drama.chief_producer,
        producer: drama.producer,
        coProducer: drama.co_producer,
        director: drama.director,
        scriptwriter: drama.scriptwriter,
        supervisor: drama.supervisor,
        coordinator: drama.coordinator
      },
      // 制作进度信息
      productionSchedule: {
        preProduction: drama.schedule_pre_production,
        filming: drama.schedule_filming,
        postProduction: drama.schedule_post_production,
        expectedReleaseDate: drama.expected_release_date
      },
      // 募资信息
      fundingInfo: {
        fundingGoal: parseFloat(drama.funding_goal) || 0,
        currentFunding: parseFloat(drama.current_funding) || 0,
        fundingEndDate: drama.funding_end_date,
        fundingShare: parseFloat(drama.funding_share) || 0,
        minInvestment: parseFloat(drama.min_investment) || 0,
        roi: parseFloat(drama.roi) || 0,
        status: drama.status || 'draft'
      },
      // 其他信息
      additionalInfo: {
        riskManagement: drama.risk_management ? JSON.parse(drama.risk_management) : [],
        confirmedResources: drama.confirmed_resources ? JSON.parse(drama.confirmed_resources) : [],
        investmentTiers: drama.investment_tiers ? JSON.parse(drama.investment_tiers) : []
      },
      // 素材信息
      materials: materialsResult.map((material: any) => ({
        id: material.id,
        dramaId: material.drama_id,
        title: material.title,
        url: material.url,
        thumbnail: material.thumbnail,
        type: material.type,
        sortOrder: material.sort_order,
        createdAt: material.created_at,
        updatedAt: material.updated_at
      })),
      // 文档信息
      documents: documentsResult.map((document: any) => ({
        id: document.id,
        dramaId: document.drama_id,
        name: document.name,
        fileUrl: document.file_url,
        fileType: document.file_type,
        fileSize: document.file_size,
        createdAt: document.created_at,
        updatedAt: document.updated_at
      })),
      // 投资权益档位
      investmentTiers: investmentTiersResult.map((tier: any) => ({
        id: tier.id,
        dramaId: tier.drama_id,
        tierName: tier.tier_name,
        minAmount: parseFloat(tier.min_amount) || 0,
        maxAmount: tier.max_amount ? parseFloat(tier.max_amount) : null,
        benefits: tier.benefits,
        returnRate: tier.return_rate ? parseFloat(tier.return_rate) : null,
        limitedQuantity: tier.limited_quantity,
        soldQuantity: tier.sold_quantity || 0,
        sortOrder: tier.sort_order || 0,
        isActive: tier.is_active === 1,
        createdAt: tier.created_at,
        updatedAt: tier.updated_at
      })),
      // 其他信息
      additionalInfo: additionalInfoResult.length > 0 ? {
        id: additionalInfoResult[0].id,
        dramaId: additionalInfoResult[0].drama_id,
        riskManagement: additionalInfoResult[0].risk_management ? JSON.parse(additionalInfoResult[0].risk_management) : [],
        confirmedResources: additionalInfoResult[0].confirmed_resources ? JSON.parse(additionalInfoResult[0].confirmed_resources) : [],
        createdAt: additionalInfoResult[0].created_at,
        updatedAt: additionalInfoResult[0].updated_at
      } : null
    };

    return {
      success: true,
      data: formattedDrama
    };

  } catch (error: any) {
    logger.error('获取短剧详情失败', {
      error: error.message,
      dramaId: getRouterParam(event, 'id'),
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
