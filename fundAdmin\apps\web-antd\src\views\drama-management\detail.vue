<template>
  <!-- 短剧详情卡片 -->
  <div class="card-box p-6">
    <!-- 加载中状态 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <Spin />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <div class="text-red-500 mb-4">{{ error }}</div>
      <AButton @click="loadDramaDetail">重新加载</AButton>
    </div>

    <!-- 短剧详情内容 -->
    <template v-else-if="drama">
      <!-- 短剧标题和基本信息 -->
      <div class="mb-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h1 class="text-2xl font-bold mb-2">
              {{ drama.title }}
              <span class="text-lg text-gray-500 font-normal ml-2">(ID: {{ drama.id }})</span>
            </h1>
          </div>
          <div class="flex space-x-2">
            <AButton @click="goBack" type="default">
              <template #icon>
                <ArrowLeft class="size-4" />
              </template>
              返回短剧列表
            </AButton>
            <AButton @click="handleEdit" type="primary">
              编辑短剧
            </AButton>
          </div>
        </div>
      </div>

      <!-- 详情导航 -->
      <div class="border-b border-gray-200 mb-6">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :class="[
              'py-2 px-1 border-b-2 font-medium text-sm',
              activeTab === tab.key
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            {{ tab.name }}
          </button>
        </nav>
      </div>

      <!-- 详情内容区域 -->
      <!-- 基本信息 -->
      <div v-if="activeTab === 'basic'">
        <BasicInfo :drama="drama" />
      </div>

      <!-- 剧本和计划书 -->
      <div v-if="activeTab === 'documents'">
        <DocumentsInfo :documents="drama.documents || []" />
      </div>

      <!-- 创作团队 -->
      <div v-if="activeTab === 'team'">
        <TeamInfo :drama="drama" />
      </div>



      <!-- 募资详情 -->
      <div v-if="activeTab === 'funding'">
        <FundingInfo :drama="drama" />
      </div>

      <!-- 投资权益 -->
      <div v-if="activeTab === 'investment'">
        <InvestmentTiersInfo :drama="drama" />
      </div>

      <!-- 制作排期 -->
      <div v-if="activeTab === 'schedule'">
        <ScheduleInfo :drama="drama" />
      </div>

      <!-- 项目素材 -->
      <div v-if="activeTab === 'materials'">
        <MaterialsInfo :drama="drama" />
      </div>

      <!-- 其他信息 -->
      <div v-if="activeTab === 'other'">
        <OtherInfo :drama="drama" />
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';

import { Spin, Button as AButton } from 'ant-design-vue';
import { ArrowLeft } from '@vben/icons';
import { getDramaDetail } from '#/api/drama-management';

// 导入详情组件
import BasicInfo from './components/BasicInfo.vue';
import MaterialsInfo from './components/MaterialsInfo.vue';
import DocumentsInfo from './components/DocumentsInfo.vue';
import TeamInfo from './components/TeamInfo.vue';
import ScheduleInfo from './components/ScheduleInfo.vue';
import FundingInfo from './components/FundingInfo.vue';
import InvestmentTiersInfo from './components/InvestmentTiersInfo.vue';
import OtherInfo from './components/OtherInfo.vue';

// 定义props
interface Props {
  dramaId: number | null;
}

const props = defineProps<Props>();

// 定义emits
const emit = defineEmits<{
  backToList: [];
  editDrama: [dramaId: number];
}>();

// 状态
const loading = ref(false);
const error = ref('');
const drama = ref<any>(null);
const activeTab = ref('basic');

// 标签页配置
const tabs = [
  { key: 'basic', name: '基本信息' },
  { key: 'materials', name: '项目素材' },
  { key: 'documents', name: '剧本和计划书' },
  { key: 'team', name: '创作团队' },
  { key: 'schedule', name: '制作排期' },
  { key: 'funding', name: '募资详情' },
  { key: 'investment', name: '投资权益' },
  { key: 'other', name: '其他信息' },
];

// 加载短剧详情
const loadDramaDetail = async () => {
  if (!props.dramaId) {
    error.value = '短剧ID不能为空';
    loading.value = false;
    return;
  }

  try {
    loading.value = true;
    error.value = '';

    const response = await getDramaDetail(props.dramaId);

    // 由于响应拦截器已经处理了数据格式，response 直接是 data 部分
    drama.value = response;
  } catch (err: any) {
    console.error('加载短剧详情失败:', err);
    error.value = err.message || '加载短剧详情失败';
  } finally {
    loading.value = false;
  }
};

// 返回短剧列表
const goBack = () => {
  emit('backToList');
};

// 编辑短剧
const handleEdit = () => {
  if (drama.value) {
    emit('editDrama', drama.value.id);
  }
};

// 监听dramaId变化
watch(() => props.dramaId, (newDramaId) => {
  if (newDramaId) {
    loadDramaDetail();
  }
}, { immediate: true });

onMounted(() => {
  if (props.dramaId) {
    loadDramaDetail();
  }
});
</script>

<style scoped>
/* 让短剧详情页面的字体更黑 */
.card-box {
  color: #1f2937; /* text-gray-800 */
}

.card-box h1,
.card-box h2,
.card-box h3,
.card-box h4 {
  color: #111827; /* text-gray-900 */
}

/* 表单项标签字体加深 */
:deep(.ant-form-item-label > label) {
  color: #374151 !important; /* text-gray-700 */
  font-weight: 500;
}

/* 输入框文字加深 */
:deep(.ant-input[disabled]),
:deep(.ant-input.input-disabled),
:deep(.ant-input.textarea-disabled) {
  color: #1f2937 !important; /* text-gray-800 */
}

/* 导航标签文字加深 */
.card-box nav button {
  color: #374151; /* text-gray-700 */
}

.card-box nav button:hover {
  color: #111827; /* text-gray-900 */
}
</style>