import { writeFile } from 'node:fs/promises';

/**
 * 短剧文档上传接口
 * POST /api/admin/dramas/[id]/documents/upload
 */
export default defineEventHandler(async (event) => {
  try {
    // 验证管理员权限
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 获取短剧ID
    const dramaId = getRouterParam(event, 'id');
    if (!dramaId || isNaN(Number(dramaId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的短剧ID'
      });
    }

    // 检查短剧是否存在
    const dramaExists = await query(
      'SELECT id FROM drama_series WHERE id = ?',
      [dramaId]
    );

    if (dramaExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '短剧不存在'
      });
    }

    // 解析multipart/form-data
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '未找到上传文件'
      });
    }

    // 查找文件数据
    const fileData = formData.find(item => item.name === 'file');
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: '文件数据无效'
      });
    }

    // 验证文件类型
    const allowedTypes = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.txt', '.rtf'];
    const fileExt = '.' + fileData.filename.split('.').pop()?.toLowerCase();
    
    if (!allowedTypes.includes(fileExt)) {
      throw createError({
        statusCode: 400,
        statusMessage: `不支持的文件类型，请上传 ${allowedTypes.join(', ')} 格式的文件`
      });
    }

    // 验证文件大小 (最大50MB)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: '文件大小不能超过50MB'
      });
    }

    // 生成唯一文件名
    const uniqueFilename = generateUniqueFilename(fileData.filename);

    // 创建临时文件
    const tempPath = await getUploadPath(uniqueFilename, 'temp');
    await writeFile(tempPath, fileData.data);

    try {
      // 指定目标路径为mengtutv/drama-documents文件夹
      const destPath = `mengtutv/drama-documents/drama_${dramaId}_${uniqueFilename}`;
      
      // 上传到COS
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || 'application/octet-stream'
      }, destPath);

      // 删除临时文件
      await deleteFile(tempPath);

      // 自动识别文件类型
      const fileTypeMap: Record<string, string> = {
        '.pdf': 'PDF',
        '.doc': 'DOC',
        '.docx': 'DOCX',
        '.ppt': 'PPT',
        '.pptx': 'PPTX',
        '.txt': 'TXT',
        '.rtf': 'RTF'
      };

      // 格式化文件大小
      const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      };

      // 提取文档名称（去掉扩展名）
      const documentName = fileData.filename.replace(/\.[^/.]+$/, '');

      // 记录审计日志
      await logAuditAction({
        action: 'ADMIN_UPLOAD_DRAMA_DOCUMENT',
        description: `管理员上传短剧文档: 短剧ID=${dramaId}`,
        userId: adminPayload.id,
        username: adminPayload.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, 'user-agent') || '',
        metadata: {
          dramaId: Number(dramaId),
          originalName: fileData.filename,
          fileName: `drama_${dramaId}_${uniqueFilename}`,
          fileSize: fileData.data.length,
          fileType: fileTypeMap[fileExt] || fileExt.toUpperCase(),
          url: uploadResult.url
        }
      });

      logger.info('短剧文档上传成功', {
        adminId: adminPayload.id,
        adminUsername: adminPayload.username,
        dramaId: Number(dramaId),
        originalName: fileData.filename,
        fileName: `drama_${dramaId}_${uniqueFilename}`,
        fileSize: fileData.data.length,
        url: uploadResult.url
      });

      return {
        success: true,
        message: '短剧文档上传成功',
        data: {
          name: documentName,
          fileUrl: uploadResult.url,
          fileType: fileTypeMap[fileExt] || fileExt.toUpperCase(),
          fileSize: formatFileSize(fileData.data.length),
          originalName: fileData.filename,
          filename: `drama_${dramaId}_${uniqueFilename}`,
          size: fileData.data.length
        }
      };

    } catch (uploadError: any) {
      // 删除临时文件
      await deleteFile(tempPath);
      
      logger.error('短剧文档上传到COS失败', {
        error: uploadError.message,
        adminId: adminPayload.id,
        dramaId: Number(dramaId),
        filename: fileData.filename
      });

      throw createError({
        statusCode: 500,
        statusMessage: `文件上传失败: ${uploadError.message}`
      });
    }

  } catch (error: any) {
    logger.error('短剧文档上传失败', {
      error: error.message,
      dramaId: getRouterParam(event, 'id')
    });

    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || '短剧文档上传失败'
    });
  }
});
