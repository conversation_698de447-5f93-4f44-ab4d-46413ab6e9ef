-- 剧投投数据库初始化脚本 - 基于模拟数据迁移

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS mengtu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mengtu;

-- 后台管理员表
CREATE TABLE IF NOT EXISTS admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
  email VARCHAR(100) UNIQUE COMMENT '邮箱',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  real_name VARCHAR(100) COMMENT '真实姓名',
  avatar VARCHAR(255) COMMENT '头像URL',
  home_path VARCHAR(255) COMMENT '首页路径',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  reset_token VARCHAR(255) NULL COMMENT '密码重置令牌',
  reset_token_expiry TIMESTAMP NULL COMMENT '重置令牌过期时间',
  tokens_invalidated_at TIMESTAMP NULL COMMENT '令牌失效时间',
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_phone (phone),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理员表';



-- 后台管理角色表
CREATE TABLE IF NOT EXISTS admin_roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
  code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色代码',
  description TEXT COMMENT '角色描述',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理角色表';

-- 管理员角色关联表
CREATE TABLE IF NOT EXISTS admin_role_relations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  admin_id INT NOT NULL,
  role_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES admin_roles(id) ON DELETE CASCADE,
  UNIQUE KEY unique_admin_role (admin_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色关联表';

-- 后台管理权限码表
CREATE TABLE IF NOT EXISTS admin_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限码',
  name VARCHAR(100) NOT NULL COMMENT '权限名称',
  description TEXT COMMENT '权限描述',
  module VARCHAR(50) COMMENT '所属模块',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_code (code),
  INDEX idx_module (module)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理权限码表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS admin_role_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  role_id INT NOT NULL,
  permission_code VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (role_id) REFERENCES admin_roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_code) REFERENCES admin_permissions(code) ON DELETE CASCADE,
  UNIQUE KEY unique_role_permission (role_id, permission_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 后台管理菜单表
CREATE TABLE IF NOT EXISTS admin_menus (
  id INT PRIMARY KEY,
  pid INT DEFAULT NULL COMMENT '父菜单ID',
  name VARCHAR(100) NOT NULL COMMENT '菜单名称',
  path VARCHAR(255) COMMENT '路由路径',
  component VARCHAR(255) COMMENT '组件路径',
  type ENUM('catalog', 'menu', 'button', 'embedded', 'link') NOT NULL COMMENT '菜单类型',
  status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
  auth_code VARCHAR(100) COMMENT '权限码',
  icon VARCHAR(100) COMMENT '图标',
  meta TEXT COMMENT '元数据(JSON格式)',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_pid (pid),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_auth_code (auth_code),
  FOREIGN KEY (pid) REFERENCES admin_menus(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理菜单表';

-- C端用户表（短剧募资管理系统）
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL COMMENT '用户名',
  email VARCHAR(100) UNIQUE COMMENT '邮箱',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  password VARCHAR(100) NOT NULL COMMENT '密码',
  password_hash VARCHAR(255) COMMENT '密码哈希（兼容server）',
  avatar VARCHAR(255) DEFAULT NULL COMMENT '头像',
  user_type ENUM('investor', 'producer', 'fund_manager') NOT NULL DEFAULT 'investor' COMMENT '用户类型：investor-投资者，producer-承制厂牌，fund_manager-基金管理人',
  real_name VARCHAR(100) COMMENT '真实姓名',
  company_name VARCHAR(200) COMMENT '公司名称（承制厂牌/基金管理人）',
  id_card VARCHAR(18) COMMENT '身份证号',
  business_license VARCHAR(100) COMMENT '营业执照号（企业用户）',
  status TINYINT DEFAULT 1 COMMENT '状态：1正常，0禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_phone (phone),
  INDEX idx_user_type (user_type),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧募资用户表';

-- 系统设置表（基于server/sql/init.sql）
CREATE TABLE IF NOT EXISTS system_settings (
  id INT UNSIGNED NOT NULL AUTO_INCREMENT,
  setting_key VARCHAR(64) NOT NULL COMMENT '设置键名',
  setting_value TEXT COMMENT '设置JSON值',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';



-- ================================
-- 插入基础数据 - 基于模拟数据
-- ================================

-- 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 插入后台管理角色数据
INSERT INTO admin_roles (id, name, code, description, status) VALUES
(1, '超级管理员', 'super', '系统超级管理员，拥有所有权限', 1),
(2, '系统管理员', 'admin', '系统管理员，拥有管理权限', 1),
(3, '普通管理员', 'user', '普通管理员，基础权限', 1)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入后台管理员数据 (基于MOCK_USERS - 严格按照模拟数据ID)
-- 密码都是 123456，使用bcryptjs加密
INSERT INTO admins (id, username, password, real_name, home_path, status) VALUES
(0, 'vben', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Vben', NULL, 1),
(1, 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', '/workspace', 1),
(2, 'jack', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jack', '/analytics', 1)
ON DUPLICATE KEY UPDATE username = VALUES(username);

-- 插入管理员角色关联 (基于MOCK_USERS的roles字段)
INSERT INTO admin_role_relations (admin_id, role_id) VALUES
(0, 1), -- vben -> super (id:0, roles:['super'])
(1, 2), -- admin -> admin (id:1, roles:['admin'])
(2, 3)  -- jack -> user (id:2, roles:['user'])
ON DUPLICATE KEY UPDATE admin_id = VALUES(admin_id);

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
