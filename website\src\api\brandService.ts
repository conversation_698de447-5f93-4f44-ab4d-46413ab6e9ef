import axios, { AxiosResponse } from 'axios'
import { getApiBaseUrl } from '../utils/environmentConfig'
import type { ApiResponse } from '../types'

// 厂牌相关API

/**
 * 获取公开厂牌列表
 * @param params 查询参数
 * @returns 厂牌列表
 */
export async function getPublicBrands(params: { limit?: number } = {}): Promise<AxiosResponse<ApiResponse<Brand[]>>> {
  const baseURL = getApiBaseUrl()
  return axios.get(`${baseURL}/brands`, {
    params
  })
}

// 厂牌数据类型定义
export interface Brand {
  id: number
  name: string
  logo: string
  companyName: string
  createdAt: string
}
