/**
 * 获取最近投资记录接口
 * GET /api/website/recent-investments
 * 用于首页信任锚点显示真实投资数据
 */

import { query } from '~/utils/database'

// 用户名脱敏处理函数
function maskUsername(username: string): string {
  if (!username || username.length === 0) {
    return '匿名用户'
  }
  
  if (username.length === 1) {
    return username + '*'
  }
  
  if (username.length === 2) {
    return username.charAt(0) + '*'
  }
  
  // 对于3个字符以上的用户名，保留第一个字符，其余用*替代
  return username.charAt(0) + '*'.repeat(username.length - 1)
}

// 计算相对时间显示
function getRelativeTime(createdAt: Date): string {
  const now = new Date()
  const diffMs = now.getTime() - createdAt.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  
  if (diffMinutes < 1) {
    return '刚刚'
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`
  } else if (diffMinutes < 1440) { // 24小时
    const diffHours = Math.floor(diffMinutes / 60)
    return `${diffHours}小时前`
  } else {
    const diffDays = Math.floor(diffMinutes / 1440)
    if (diffDays === 1) {
      return '昨天'
    } else if (diffDays <= 7) {
      return `${diffDays}天前`
    } else {
      return '一周前'
    }
  }
}

export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const queryParams = getQuery(event)
    const limit = parseInt(queryParams.limit as string) || 30 // 默认返回30条记录

    // 查询最近的投资记录
    // 只查询投资支出记录（shells_out + investment）且状态为completed
    const investmentQuery = `
      SELECT 
        t.amount,
        t.description,
        t.created_at,
        u.username
      FROM user_asset_transactions t
      INNER JOIN users u ON t.user_id = u.id
      WHERE t.transaction_type = 'shells_out' 
        AND t.related_type = 'investment'
        AND t.status = 'completed'
      ORDER BY t.created_at DESC
      LIMIT ?
    `

    const investmentRecords = await query(investmentQuery, [limit])

    // 处理投资记录数据
    const processedRecords = investmentRecords.map((record: any) => ({
      investor: maskUsername(record.username),
      time: getRelativeTime(new Date(record.created_at)),
      amount: parseFloat(record.amount),
      description: record.description,
      timestamp: record.created_at
    }))

    return {
      success: true,
      data: {
        records: processedRecords,
        total: processedRecords.length,
        timestamp: new Date().toISOString()
      },
      message: '获取投资记录成功'
    }

  } catch (error: any) {
    console.error('获取投资记录失败:', error)
    
    return {
      success: false,
      message: '获取投资记录失败',
      data: {
        records: [],
        total: 0,
        timestamp: new Date().toISOString()
      },
      error: error.message
    }
  }
})
