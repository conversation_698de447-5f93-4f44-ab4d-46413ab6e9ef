// 新闻管理相关类型定义

export namespace NewsManagementApi {
  // 新闻状态枚举
  export type NewsStatus = 'draft' | 'pending' | 'published' | 'archived';

  // 新闻基本信息
  export interface News {
    id: number;
    title: string;
    summary?: string;
    content: string;
    coverImage?: string;
    author?: string;
    sourceUrl?: string;
    status: NewsStatus;
    isFeatured: boolean;
    viewCount: number;
    publishDate?: string;
    createdAt: string;
    updatedAt: string;
    category?: NewsCategory;
    tags?: NewsTag[];
    seo?: NewsSeo;
  }

  // 新闻分类
  export interface NewsCategory {
    id: number;
    name: string;
    slug: string;
    description?: string;
    parentId?: number;
    sortOrder: number;
    isActive: boolean;
    newsCount?: number;
    createdAt: string;
    updatedAt: string;
    children?: NewsCategory[];
  }

  // 新闻标签
  export interface NewsTag {
    id: number;
    name: string;
    usageCount?: number;
    createdAt: string;
  }

  // 新闻SEO信息
  export interface NewsSeo {
    newsId: number;
    metaTitle?: string;
    metaDescription?: string;
    metaKeywords?: string;
    canonicalUrl?: string;
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
  }

  // 新闻列表查询参数
  export interface NewsListParams {
    page?: number;
    pageSize?: number;
    status?: NewsStatus;
    category?: number;
    search?: string;
    author?: string;
    featured?: boolean;
    startDate?: string;
    endDate?: string;
    orderBy?: string;
    orderDirection?: 'ASC' | 'DESC';
  }

  // 新闻列表响应
  export interface NewsListResponse {
    list: News[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  }

  // 新闻详情响应
  export interface NewsDetailResponse {
    data: News;
  }

  // 创建新闻参数
  export interface CreateNewsParams {
    title: string;
    summary?: string;
    content: string;
    cover_image_url?: string;
    category_id?: number;
    author?: string;
    source_url?: string;
    status?: NewsStatus;
    is_featured?: boolean;
    publish_date?: string;
    tags?: string[];
    seo?: {
      meta_title?: string;
      meta_description?: string;
      meta_keywords?: string;
      canonical_url?: string;
      og_title?: string;
      og_description?: string;
      og_image?: string;
    };
  }

  // 更新新闻参数
  export interface UpdateNewsParams extends Partial<CreateNewsParams> {
    // 继承创建参数，所有字段都是可选的
  }

  // 发布新闻参数
  export interface PublishNewsParams {
    action: 'publish' | 'unpublish' | 'archive';
    publish_date?: string;
  }

  // 分类列表查询参数
  export interface CategoryListParams {
    page?: number;
    pageSize?: number;
    search?: string;
    parentId?: number;
    isActive?: boolean;
  }

  // 分类列表响应
  export interface CategoryListResponse {
    list: NewsCategory[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  }

  // 创建分类参数
  export interface CreateCategoryParams {
    name: string;
    slug: string;
    description?: string;
    parent_id?: number;
    sort_order?: number;
    is_active?: boolean;
  }

  // 更新分类参数
  export interface UpdateCategoryParams extends Partial<CreateCategoryParams> {
    // 继承创建参数，所有字段都是可选的
  }

  // 标签列表查询参数
  export interface TagListParams {
    page?: number;
    pageSize?: number;
    search?: string;
  }

  // 标签列表响应
  export interface TagListResponse {
    list: NewsTag[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  }

  // 创建标签参数
  export interface CreateTagParams {
    name: string;
  }

  // 操作响应
  export interface OperationResponse {
    success: boolean;
    message: string;
    data?: any;
  }

  // 上传响应
  export interface UploadResponse {
    success: boolean;
    message: string;
    data: {
      url: string;
      filename: string;
      originalName: string;
      size: number;
    };
  }

  // 统计数据
  export interface NewsStats {
    totalNews: number;
    publishedNews: number;
    draftNews: number;
    pendingNews: number;
    archivedNews: number;
    totalViews: number;
    todayViews: number;
    totalCategories: number;
    totalTags: number;
  }

  // 批量操作参数
  export interface BatchOperationParams {
    ids: number[];
    action: 'delete' | 'publish' | 'unpublish' | 'archive' | 'feature' | 'unfeature';
  }
}
