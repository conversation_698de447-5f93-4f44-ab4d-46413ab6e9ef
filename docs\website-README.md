# 剧投投 - 前端应用

## 技术栈

- Vue 3
- Vite
- Pinia (状态管理)
- Vue Router
- Tailwind CSS
- Axios (API请求)

## 项目结构

```
frontend/
├── .env                # 默认环境配置
├── .env.development    # 开发环境配置
├── .env.production     # 生产环境配置
├── src/                # 源代码
│   ├── assets/         # 静态资源
│   ├── components/     # Vue组件
│   ├── utils/          # 工具函数
│   ├── views/          # 页面视图
│   ├── router/         # 路由配置
│   ├── store/          # Pinia状态
│   └── main.js         # 应用入口
├── public/             # 公共静态文件
├── vite.config.js      # Vite配置
└── package.json        # 依赖和脚本
```

## 环境配置

前端环境配置文件:
- `.env` - 默认环境变量
- `.env.development` - 开发环境变量
- `.env.production` - 生产环境变量

所有以 `VITE_` 开头的变量都可以在前端代码中通过 `import.meta.env.VITE_XXX` 访问。

### 环境变量列表

- `VITE_API_BASE_URL` - API服务器地址
- `VITE_APP_TITLE` - 应用标题
- `VITE_APP_DESCRIPTION` - 应用描述

## 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 构建生产版本（明确指定环境）
npm run build:prod

# 预览构建结果
npm run preview

# 代码检查
npm run lint
```

## 环境切换

前端环境可以通过多种方式切换：

1. **构建时通过命令行参数**：
   ```bash
   # 开发环境构建（默认使用.env.development文件）
   npm run build -- --mode development
   
   # 生产环境构建（使用.env.production文件）
   npm run build -- --mode production
   
   # 自定义环境构建（需要创建.env.staging文件）
   npm run build -- --mode staging
   ```

2. **开发服务器启动时指定环境**：
   ```bash
   # 使用开发环境（默认）
   npm run dev

   # 使用生产环境配置启动开发服务器
   npm run dev -- --mode production
   
   # 使用自定义环境配置启动开发服务器
   npm run dev -- --mode staging
   ```

3. **通过代码中的环境配置**：
   修改 `src/utils/environmentConfig.js` 文件中的 `ACTIVE_ENVIRONMENT` 常量：
   ```js
   // 可选值: 'development', 'testing', 'production'
   const ACTIVE_ENVIRONMENT = 'production'; // 改为您需要的环境
   ```

4. **创建自定义环境文件**：
   您可以创建自定义环境配置文件，例如`.env.staging`：
   ```
   # 前端测试环境配置
   NODE_ENV=production
   VITE_API_BASE_URL=http://staging-server:3001/api
   VITE_APP_TITLE=剧投投(测试版)
   VITE_APP_DESCRIPTION=中国最专业的短剧投资平台
   ```

5. **修改环境变量**：
   - 修改API服务器地址：打开对应环境文件（如`.env.development`）编辑`VITE_API_BASE_URL`
   - 修改应用标题：编辑`VITE_APP_TITLE`
   - 添加新的环境变量：添加以`VITE_`开头的新变量

### 查看当前环境

您可以通过以下方法查看当前运行环境：

1. **通过浏览器控制台**：
   在浏览器开发者工具的控制台中输入：
   ```js
   console.log(import.meta.env)
   ```

2. **在Vue组件中**：
   ```js
   console.log('当前环境:', import.meta.env.MODE)
   console.log('API地址:', import.meta.env.VITE_API_BASE_URL)
   ```

3. **查看启动日志**：
   启动开发服务器时，Vite会在控制台输出当前使用的环境配置文件。

### 环境优先级

环境变量的优先级从高到低为：
1. 命令行指定的环境变量（如`--mode production`）
2. `.env.[mode]`文件中的环境变量
3. `.env`文件中的环境变量
4. `environmentConfig.js`中的默认配置
