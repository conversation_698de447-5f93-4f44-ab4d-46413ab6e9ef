/**
 * 网站首页统计数据API
 * 功能描述：获取网站首页的统计数据
 * 入参：无
 * 返回参数：统计数据对象
 * url地址：/api/website/stats
 * 请求方式：GET
 */

import { request } from '../utils/request'

// 网站统计数据类型定义
export interface WebsiteStats {
  // 已募资金总额（万元）
  totalRaisedAmount: number
  // 募资金额增长率
  fundingGrowthRate: number
  // 今日投资总额（万元）
  todayInvestmentAmount: number
  // 今日投资占比（数字，不是百分比）
  todayInvestmentRatio: number
  // 历史投资总额（万元）
  historicalInvestmentTotal: number
  // 总投资人数
  totalInvestors: number
  // 用户列表（用于实时在线显示）
  users: Array<{
    username: string
    userType: string
  }>
}

// 投资记录类型定义
export interface InvestmentRecord {
  investor: string      // 脱敏后的投资者姓名
  time: string         // 相对时间显示
  amount: number       // 投资金额
  description: string  // 投资描述
  timestamp: string    // 原始时间戳
}

// 投资记录响应类型
export interface RecentInvestmentsResponse {
  records: InvestmentRecord[]
  total: number
  timestamp: string
}

/**
 * 获取网站首页统计数据
 * 功能描述：获取已募资金总额、今日投资总额、投资人数等统计数据
 * 入参：无
 * 返回参数：WebsiteStats统计数据对象
 * url地址：/api/website/stats
 * 请求方式：GET
 */
export const getWebsiteStats = (): Promise<{
  success: boolean
  data: WebsiteStats
  message?: string
  code?: number
}> => {
  return request({
    url: '/website/stats',
    method: 'GET'
  })
}

/**
 * 获取最近投资记录
 * 功能描述：获取真实的投资交易记录，用于首页信任锚点显示
 * 入参：limit - 返回记录数量限制（可选，默认30）
 * 返回参数：RecentInvestmentsResponse投资记录数据
 * url地址：/api/website/recent-investments
 * 请求方式：GET
 */
export const getRecentInvestments = (limit?: number): Promise<{
  success: boolean
  data: RecentInvestmentsResponse
  message?: string
  code?: number
}> => {
  return request({
    url: '/website/recent-investments',
    method: 'GET',
    params: limit ? { limit } : undefined
  })
}
