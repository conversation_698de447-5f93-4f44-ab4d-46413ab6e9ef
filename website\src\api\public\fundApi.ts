import axios, { AxiosResponse } from 'axios'
import { getCompleteApiUrl } from '../../utils/environmentConfig'
import type { ApiResponse, Fund, Pagination } from '../../types'

// 公开基金查询参数类型
export interface PublicFundsParams {
  page?: number
  pageSize?: number
  type?: string | string[]
  risk?: string | string[]
  period?: string | string[]
  [key: string]: any
}

// 公开基金列表响应类型
export interface PublicFundsResponse {
  funds: Fund[]
  pagination: Pagination
}

// 基金类型选项
export interface FundTypeOption {
  id: string
  value: string
  label: string
}

// 风险等级选项
export interface RiskLevelOption {
  id: string
  value: string
  label: string
  color: string
}

// 期限类型选项
export interface PeriodTypeOption {
  id: string
  value: string
  label: string
}

// 使用统一的API URL获取函数
const baseURL = getCompleteApiUrl()

// 获取公开基金列表 - 支持分页和筛选
export const getPublicFunds = async (params: PublicFundsParams = {}): Promise<AxiosResponse<ApiResponse<PublicFundsResponse>>> => {
  const { page = 1, pageSize = 10, type, risk, period, ...otherParams } = params

  const queryParams = new URLSearchParams()
  queryParams.append('page', page.toString())
  queryParams.append('pageSize', pageSize.toString())
  
  // 添加可选筛选条件 - 支持多选（数组）
  if (type) {
    if (Array.isArray(type)) {
      // 如果是数组，添加多个同名参数
      type.forEach(value => queryParams.append('type', value))
    } else {
      queryParams.append('type', type)
    }
  }
  
  if (risk) {
    if (Array.isArray(risk)) {
      risk.forEach(value => queryParams.append('risk', value))
    } else {
      queryParams.append('risk', risk)
    }
  }
  
  if (period) {
    if (Array.isArray(period)) {
      period.forEach(value => queryParams.append('period', value))
    } else {
      queryParams.append('period', period)
    }
  }
  
  // 添加其他参数
  for (const key in otherParams) {
    if (otherParams[key] !== undefined && otherParams[key] !== null) {
      queryParams.append(key, otherParams[key].toString())
    }
  }
  
  return axios.get(`${baseURL}/funds?${queryParams.toString()}`)
}

// 获取单个基金详情
export const getPublicFundByCode = async (code: string): Promise<AxiosResponse<ApiResponse<Fund>>> => {
  return axios.get(`${baseURL}/funds/${code}`)
}

// 获取基金类型列表
export const getFundTypes = async (): Promise<FundTypeOption[]> => {
  return [
    { id: 'equity', value: 'equity', label: '股权型' },
    { id: 'debt', value: 'debt', label: '债权型' },
    { id: 'mixed', value: 'mixed', label: '混合型' }
  ]
}

// 获取风险等级列表
export const getRiskLevels = async (): Promise<RiskLevelOption[]> => {
  return [
    { id: 'R1', value: 'R1', label: '低风险', color: 'bg-green-500' },
    { id: 'R2', value: 'R2', label: '较低风险', color: 'bg-blue-500' },
    { id: 'R3', value: 'R3', label: '中等风险', color: 'bg-yellow-500' },
    { id: 'R4', value: 'R4', label: '较高风险', color: 'bg-orange-500' },
    { id: 'R5', value: 'R5', label: '高风险', color: 'bg-red-500' }
  ]
}

// 获取封闭期类型列表
export const getPeriodTypes = async (): Promise<PeriodTypeOption[]> => {
  return [
    { id: 'short', value: 'short', label: '短期（≤1年）' },
    { id: 'medium', value: 'medium', label: '中期（1-3年）' },
    { id: 'long', value: 'long', label: '长期（>3年）' }
  ]
}

// 获取基金筛选选项（组合API）
export const getFundFilterOptions = async () => {
  const [types, risks, periods] = await Promise.all([
    getFundTypes(),
    getRiskLevels(),
    getPeriodTypes()
  ])
  
  return {
    types,
    risks,
    periods
  }
}
