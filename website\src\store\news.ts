import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  NewsItem, 
  NewsCategory, 
  NewsTag, 
  NewsListParams,
  NewsListResponse,
  CategoriesResponse
} from '@/api/news'
import {
  getNewsList,
  getNewsDetail,
  getNewsCategories,
  increaseNewsView,
  getFeaturedNews,
  getLatestNews,
  getNewsByCategory,
  searchNews,
  getPopularNews
} from '@/api/news'

export const useNewsStore = defineStore('news', () => {
  // 状态
  const newsList = ref<NewsItem[]>([])
  const newsDetail = ref<NewsItem | null>(null)
  const categories = ref<NewsCategory[]>([])
  const flatCategories = ref<NewsCategory[]>([])
  const popularTags = ref<NewsTag[]>([])
  const featuredNews = ref<NewsItem[]>([])
  const latestNews = ref<NewsItem[]>([])
  const popularNews = ref<NewsItem[]>([])
  
  // 分页信息
  const pagination = ref({
    page: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0
  })
  
  // 加载状态
  const loading = ref(false)
  const detailLoading = ref(false)
  const categoriesLoading = ref(false)
  
  // 错误状态
  const error = ref<string | null>(null)
  const detailError = ref<string | null>(null)
  
  // 计算属性
  const hasNews = computed(() => newsList.value.length > 0)
  const hasCategories = computed(() => categories.value.length > 0)
  const hasFeaturedNews = computed(() => featuredNews.value.length > 0)
  
  // 获取新闻列表
  const fetchNewsList = async (params: NewsListParams = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await getNewsList(params)
      
      if (response.success && response.data) {
        newsList.value = response.data.list
        pagination.value = response.data.pagination
      } else {
        throw new Error(response.message || '获取新闻列表失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取新闻列表失败'
      console.error('获取新闻列表失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  // 获取新闻详情
  const fetchNewsDetail = async (id: number) => {
    detailLoading.value = true
    detailError.value = null
    
    try {
      const response = await getNewsDetail(id)
      
      if (response.success && response.data) {
        newsDetail.value = response.data
        
        // 自动增加阅读量
        try {
          await increaseNewsView(id)
          // 更新本地阅读量
          if (newsDetail.value) {
            newsDetail.value.viewCount += 1
          }
        } catch (viewErr) {
          console.warn('增加阅读量失败:', viewErr)
        }
      } else {
        throw new Error(response.message || '获取新闻详情失败')
      }
    } catch (err: any) {
      detailError.value = err.message || '获取新闻详情失败'
      console.error('获取新闻详情失败:', err)
    } finally {
      detailLoading.value = false
    }
  }
  
  // 获取分类列表
  const fetchCategories = async () => {
    categoriesLoading.value = true
    
    try {
      const response = await getNewsCategories()
      
      if (response.success && response.data) {
        categories.value = response.data.categories
        flatCategories.value = response.data.flatCategories
        popularTags.value = response.data.popularTags
      } else {
        throw new Error(response.message || '获取分类列表失败')
      }
    } catch (err: any) {
      console.error('获取分类列表失败:', err)
    } finally {
      categoriesLoading.value = false
    }
  }
  
  // 获取推荐新闻
  const fetchFeaturedNews = async (limit: number = 6) => {
    try {
      const response = await getFeaturedNews(limit)
      
      if (response.success && response.data) {
        featuredNews.value = response.data.list
      }
    } catch (err: any) {
      console.error('获取推荐新闻失败:', err)
    }
  }
  
  // 获取最新新闻
  const fetchLatestNews = async (limit: number = 10) => {
    try {
      const response = await getLatestNews(limit)
      
      if (response.success && response.data) {
        latestNews.value = response.data.list
      }
    } catch (err: any) {
      console.error('获取最新新闻失败:', err)
    }
  }
  
  // 获取热门新闻
  const fetchPopularNews = async (limit: number = 10) => {
    try {
      const response = await getPopularNews(limit)
      
      if (response.success && response.data) {
        popularNews.value = response.data.list
      }
    } catch (err: any) {
      console.error('获取热门新闻失败:', err)
    }
  }
  
  // 根据分类获取新闻
  const fetchNewsByCategory = async (category: string, params: Omit<NewsListParams, 'category'> = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await getNewsByCategory(category, params)
      
      if (response.success && response.data) {
        newsList.value = response.data.list
        pagination.value = response.data.pagination
      } else {
        throw new Error(response.message || '获取分类新闻失败')
      }
    } catch (err: any) {
      error.value = err.message || '获取分类新闻失败'
      console.error('获取分类新闻失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  // 搜索新闻
  const searchNewsList = async (keyword: string, params: Omit<NewsListParams, 'search'> = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await searchNews(keyword, params)
      
      if (response.success && response.data) {
        newsList.value = response.data.list
        pagination.value = response.data.pagination
      } else {
        throw new Error(response.message || '搜索新闻失败')
      }
    } catch (err: any) {
      error.value = err.message || '搜索新闻失败'
      console.error('搜索新闻失败:', err)
    } finally {
      loading.value = false
    }
  }
  
  // 清空新闻详情
  const clearNewsDetail = () => {
    newsDetail.value = null
    detailError.value = null
  }
  
  // 清空新闻列表
  const clearNewsList = () => {
    newsList.value = []
    error.value = null
    pagination.value = {
      page: 1,
      pageSize: 12,
      total: 0,
      totalPages: 0
    }
  }
  
  // 重置所有状态
  const resetStore = () => {
    clearNewsList()
    clearNewsDetail()
    categories.value = []
    flatCategories.value = []
    popularTags.value = []
    featuredNews.value = []
    latestNews.value = []
    popularNews.value = []
    loading.value = false
    detailLoading.value = false
    categoriesLoading.value = false
  }
  
  return {
    // 状态
    newsList,
    newsDetail,
    categories,
    flatCategories,
    popularTags,
    featuredNews,
    latestNews,
    popularNews,
    pagination,
    loading,
    detailLoading,
    categoriesLoading,
    error,
    detailError,
    
    // 计算属性
    hasNews,
    hasCategories,
    hasFeaturedNews,
    
    // 方法
    fetchNewsList,
    fetchNewsDetail,
    fetchCategories,
    fetchFeaturedNews,
    fetchLatestNews,
    fetchPopularNews,
    fetchNewsByCategory,
    searchNewsList,
    clearNewsDetail,
    clearNewsList,
    resetStore
  }
})
