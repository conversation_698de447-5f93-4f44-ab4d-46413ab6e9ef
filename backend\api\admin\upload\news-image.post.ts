import { writeFile } from 'fs/promises';
import { logger, logAuditAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';
import {
  getFileExtension,
  validateFileType,
  formatFileSize,
  generateUniqueFilename,
  getUploadPath,
  deleteFile
} from '~/utils/file-utils';
import { uploadToCOS } from '~/utils/cos-uploader';

/**
 * 管理员上传新闻图片接口
 * POST /api/admin/upload/news-image
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法上传新闻图片'
      });
    }

    // 获取上传的文件
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '未选择文件'
      });
    }

    const fileData = formData.find(item => item.name === 'file');
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: '文件数据无效'
      });
    }

    // 验证文件类型（只允许图片）
    const allowedTypes = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const fileExt = getFileExtension(fileData.filename);
    
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `新闻图片只支持图片格式: ${allowedTypes.join(', ')}`
      });
    }

    // 验证文件大小（5MB限制）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `新闻图片大小超过限制，最大允许 ${formatFileSize(maxSize)}`
      });
    }

    // 生成唯一文件名
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    
    // 保存到临时目录
    const tempPath = await getUploadPath(uniqueFilename, 'temp');
    await writeFile(tempPath, fileData.data);

    try {
      // 指定目标路径为mengtutv/news文件夹
      const destPath = `mengtutv/news/${uniqueFilename}`;
      
      // 上传到COS
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || 'image/jpeg'
      }, destPath);

      // 删除临时文件
      await deleteFile(tempPath);

      // 记录审计日志
      await logAuditAction({
        action: 'ADMIN_UPLOAD_NEWS_IMAGE',
        description: `管理员上传新闻图片: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, 'user-agent') || '',
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          url: uploadResult.url
        }
      });

      logger.info('管理员上传新闻图片成功', {
        adminId: admin.id,
        adminUsername: admin.username,
        originalName: fileData.filename,
        url: uploadResult.url
      });

      return {
        success: true,
        message: '新闻图片上传成功',
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalName: fileData.filename,
          size: fileData.data.length
        }
      };

    } catch (uploadError: any) {
      // 删除临时文件
      await deleteFile(tempPath);
      
      logger.error('新闻图片上传到COS失败', {
        error: uploadError.message,
        adminId: admin.id,
        filename: fileData.filename
      });

      throw createError({
        statusCode: 500,
        statusMessage: `新闻图片上传失败: ${uploadError.message}`
      });
    }

  } catch (error: any) {
    logger.error('管理员上传新闻图片失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
