# 剧投投募资管理系统 - 综合项目指南

## 项目概述

剧投投募资管理系统是一个完整的短剧投资平台解决方案，采用现代化的微服务架构，包含用户端网站、管理后台和后端API服务。系统专为短剧行业的投资、制作和管理需求而设计。

## 项目整体架构

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    剧投投募资管理系统                        │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Frontend Layer)                                    │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   用户端网站      │  │   管理后台       │                   │
│  │   (website)     │  │  (fundAdmin)    │                   │
│  │   Vue 3         │  │  Vben Admin     │                   │
│  │   Port: 3000    │  │  Port: 5173     │                   │
│  └─────────────────┘  └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  API层 (API Layer)                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              后端API服务 (backend)                       │ │
│  │              Nitro Framework                            │ │
│  │              Port: 3001                                 │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据层 (Data Layer)                                        │
│  ┌─────────────────┐  ┌─────────────────┐                   │
│  │   MySQL 8.0+    │  │   文件存储       │                   │
│  │   Port: 3306    │  │   腾讯云COS/     │                   │
│  │   Database      │  │   阿里云OSS      │                   │
│  └─────────────────┘  └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈对比

| 模块 | 框架 | 语言 | UI库 | 状态管理 | 构建工具 | 端口 |
|------|------|------|------|----------|----------|------|
| **用户端网站** | Vue 3 | TypeScript | Tailwind CSS | Pinia | Vite | 3000 |
| **管理后台** | Vue 3 | TypeScript | Ant Design Vue | Pinia | Vite | 5173 |
| **后端API** | Nitro | TypeScript | - | - | Nitro | 3001 |
| **数据库** | MySQL | SQL | - | - | - | 3306 |

## 核心模块详述

### 1. 后端API服务 (backend/)

#### 技术架构
- **框架**: Nitro (UnJS 生态系统)
- **语言**: TypeScript
- **数据库**: MySQL 8.0+ 
- **认证**: JWT + bcrypt
- **文件上传**: Multer + 云存储
- **日志系统**: 自定义日志记录

#### 目录结构
```
backend/
├── api/                    # API路由定义
│   ├── admin/             # 管理员API
│   ├── auth/              # 认证相关API
│   ├── public/            # 公开API
│   └── users/             # 用户API
├── middleware/            # 中间件
├── utils/                 # 工具函数
├── types/                 # TypeScript类型
├── scripts/               # 数据库脚本
└── nitro.config.ts        # Nitro配置
```

#### 核心功能
- **用户认证系统**: 支持邮箱/手机号登录，JWT令牌管理
- **权限管理**: 基于角色的访问控制(RBAC)
- **基金管理**: 基金产品CRUD，投资记录管理
- **内容管理**: 轮播图、新闻、公告管理
- **文件上传**: 支持腾讯云COS和阿里云OSS
- **审计日志**: 完整的操作记录和安全监控

### 2. 管理后台 (fundAdmin/)

#### 技术架构
- **框架**: Vue Vben Admin 5.0
- **语言**: TypeScript
- **UI库**: Ant Design Vue
- **状态管理**: Pinia
- **构建工具**: Vite + Turbo

#### 目录结构
```
fundAdmin/
├── apps/                   # 应用目录
│   ├── web-antd/          # Ant Design版本
│   ├── web-ele/           # Element Plus版本
│   └── web-naive/         # Naive UI版本
├── packages/              # 共享包
│   ├── @core/            # 核心功能包
│   ├── effects/          # 副作用处理
│   ├── stores/           # 状态管理
│   └── types/            # 类型定义
└── internal/             # 内部工具
```

#### 核心功能
- **用户管理**: C端用户的增删改查和状态管理
- **角色权限**: 管理员角色和权限分配
- **菜单管理**: 动态菜单配置和权限控制
- **基金管理**: 基金产品的全生命周期管理
- **内容运营**: Banner、新闻、公告的内容管理
- **系统设置**: 网站配置、邮件设置、存储配置
- **数据统计**: 用户、基金、投资数据的统计分析

### 3. 用户端网站 (website/)

#### 技术架构
- **框架**: Vue 3
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Pinia
- **路由**: Vue Router

#### 目录结构
```
website/
├── src/
│   ├── views/             # 页面组件
│   ├── components/        # 可复用组件
│   ├── api/              # API调用
│   ├── store/            # 状态管理
│   ├── router/           # 路由配置
│   ├── utils/            # 工具函数
│   └── types/            # 类型定义
├── public/               # 静态资源
└── vite.config.ts        # Vite配置
```

#### 核心功能
- **用户注册登录**: 支持投资者、承制厂牌、基金管理人注册
- **基金展示**: 基金产品列表和详情展示
- **投资功能**: 在线投资和投资记录查看
- **个人中心**: 用户信息管理和投资历史
- **新闻资讯**: 行业新闻和平台公告
- **响应式设计**: 适配PC和移动端设备

## 模块相互关系

### 数据流向
```
用户端网站 ──API调用──→ 后端API服务 ──数据操作──→ MySQL数据库
     ↑                      ↓
     └──────认证令牌──────────┘

管理后台 ──API调用──→ 后端API服务 ──数据操作──→ MySQL数据库
    ↑                     ↓
    └──────管理员认证──────┘
```

### 认证体系
- **双重认证系统**: C端用户认证和管理员认证完全分离
- **JWT令牌**: 使用不同的密钥和过期时间
- **权限控制**: 基于角色的细粒度权限管理

### 数据共享
- **用户数据**: 管理后台可以管理C端用户
- **基金数据**: 后台创建，前端展示
- **系统设置**: 后台配置，前端使用

## 完整部署指南

### 开发环境部署

#### 环境要求
- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.0.0
- **MySQL**: >= 8.0
- **Git**: 最新版本

#### 1. 项目克隆和依赖安装

```bash
# 克隆项目
git clone <repository-url>
cd ReelShortFund

# 安装后端依赖
cd backend
pnpm install

# 安装前端网站依赖
cd ../website
pnpm install

# 安装管理后台依赖
cd ../fundAdmin
pnpm install
```

#### 2. 数据库配置

**创建数据库**
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE mengtu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**执行初始化脚本**
```bash
# 回到项目根目录
cd ..

# 执行数据库初始化
mysql -u root -p < docs/sql/init-db.sql
mysql -u root -p < docs/sql/create-audit-logs.sql
mysql -u root -p < docs/sql/add-banner-menu.sql
```

#### 3. 环境变量配置

**后端环境配置 (backend/.env)**
```env
# 数据库配置
DB_HOST=*********
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=mengtu

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_ADMIN_SECRET=your-admin-jwt-secret-key

# 服务配置
PORT=3001
NODE_ENV=development

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp

# 邮件配置（可选）
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
```

**前端网站环境配置 (website/.env.development)**
```env
# API配置
VITE_API_BASE_URL=http://localhost:3001/api

# 应用配置
VITE_APP_TITLE=剧投投(开发)
VITE_APP_DESCRIPTION=中国最专业的短剧投资平台

# 开发配置
NODE_ENV=development
```

**管理后台环境配置 (fundAdmin/.env.development)**
```env
# API配置
VITE_API_URL=http://localhost:3001/api

# 应用配置
VITE_APP_TITLE=剧投投管理后台
VITE_GLOB_APP_SHORT_NAME=剧投投

# 开发配置
NODE_ENV=development
```

#### 4. 启动服务

**启动后端服务**
```bash
cd backend
pnpm dev
# 服务将在 http://localhost:3001 启动
```

**启动前端网站**
```bash
cd website
pnpm dev
# 服务将在 http://localhost:3000 启动
```

**启动管理后台**
```bash
cd fundAdmin
pnpm dev:antd
# 服务将在 http://localhost:5173 启动
```

#### 5. 验证部署

**测试后端API**
```bash
# 健康检查
curl http://localhost:3001/api/health

# 测试登录接口
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"test123456"}'
```

**测试前端访问**
- 用户端网站: http://localhost:3000
- 管理后台: http://localhost:5173

**默认管理员账号**
- 用户名: `admin`
- 密码: `123456`

### 生产环境部署（宝塔面板）

#### 1. 服务器环境准备

**宝塔面板安装**
```bash
# CentOS 7/8
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

**安装必要软件**
- **Node.js**: 18.x 或更高版本
- **MySQL**: 8.0 或更高版本
- **Nginx**: 最新稳定版
- **PM2**: 进程管理器

#### 2. 数据库部署

**在宝塔面板中创建数据库**
1. 登录宝塔面板
2. 进入"数据库"页面
3. 创建数据库 `mengtu`
4. 设置用户名和密码
5. 导入SQL脚本

**执行初始化脚本**
```bash
# 上传SQL文件到服务器
scp docs/sql/*.sql root@your-server:/www/wwwroot/sql/

# 在服务器上执行
mysql -u root -p mengtu < /www/wwwroot/sql/init-db.sql
mysql -u root -p mengtu < /www/wwwroot/sql/create-audit-logs.sql
mysql -u root -p mengtu < /www/wwwroot/sql/add-banner-menu.sql
```

#### 3. 后端服务部署

**上传代码**
```bash
# 在本地构建
cd backend
pnpm build

# 上传到服务器
scp -r .output root@your-server:/www/wwwroot/mengtu-api/
scp package.json root@your-server:/www/wwwroot/mengtu-api/
```

**配置环境变量**
```bash
# 在服务器上创建 .env 文件
cat > /www/wwwroot/mengtu-api/.env << EOF
DB_HOST=localhost
DB_PORT=3306
DB_USER=mengtu_user
DB_PASSWORD=your-database-password
DB_NAME=mengtu

JWT_SECRET=your-production-jwt-secret
JWT_ADMIN_SECRET=your-production-admin-jwt-secret

PORT=3001
NODE_ENV=production

UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp
EOF
```

**使用PM2启动服务**
```bash
# 安装PM2
npm install -g pm2

# 创建PM2配置文件
cat > /www/wwwroot/mengtu-api/ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'mengtu-api',
    script: '.output/server/index.mjs',
    cwd: '/www/wwwroot/mengtu-api',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

# 启动服务
cd /www/wwwroot/mengtu-api
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 4. 前端网站部署

**构建前端项目**
```bash
# 在本地构建
cd website
pnpm build

# 上传构建文件
scp -r dist/* root@your-server:/www/wwwroot/mengtu-web/
```

**Nginx配置**
```nginx
# 在宝塔面板中添加网站，然后配置Nginx
server {
    listen 80;
    server_name your-domain.com;
    root /www/wwwroot/mengtu-web;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 5. 管理后台部署

**构建管理后台**
```bash
# 在本地构建
cd fundAdmin
pnpm build:antd

# 上传构建文件
scp -r apps/web-antd/dist/* root@your-server:/www/wwwroot/mengtu-admin/
```

**Nginx配置**
```nginx
server {
    listen 80;
    server_name admin.your-domain.com;
    root /www/wwwroot/mengtu-admin;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 管理后台访问控制（可选）
    # allow ***********/24;
    # deny all;
}
```

#### 6. SSL证书配置

**在宝塔面板中配置SSL**
1. 进入网站设置
2. 选择"SSL"选项卡
3. 申请Let's Encrypt免费证书
4. 开启"强制HTTPS"

#### 7. 防火墙和安全配置

**开放必要端口**
```bash
# 在宝塔面板安全设置中开放
# 80 (HTTP)
# 443 (HTTPS)
# 3001 (API服务，仅内网访问)
```

**数据库安全**
```bash
# 修改MySQL默认端口
# 设置复杂密码
# 禁用远程root登录
```

### 监控和维护

#### 1. 日志监控

**PM2日志查看**
```bash
# 查看实时日志
pm2 logs mengtu-api

# 查看错误日志
pm2 logs mengtu-api --err

# 清空日志
pm2 flush
```

**Nginx日志**
```bash
# 访问日志
tail -f /www/wwwlogs/your-domain.com.log

# 错误日志
tail -f /www/wwwlogs/your-domain.com.error.log
```

#### 2. 性能监控

**PM2监控**
```bash
# 查看进程状态
pm2 status

# 查看详细信息
pm2 show mengtu-api

# 重启服务
pm2 restart mengtu-api
```

**数据库监控**
```bash
# 查看数据库状态
mysql -u root -p -e "SHOW PROCESSLIST;"

# 查看数据库大小
mysql -u root -p -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables WHERE table_schema = 'mengtu';"
```

#### 3. 备份策略

**数据库备份**
```bash
# 创建备份脚本
cat > /www/backup/backup-mengtu.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p'your-password' mengtu > /www/backup/mengtu_\$DATE.sql
find /www/backup -name "mengtu_*.sql" -mtime +7 -delete
EOF

# 设置定时任务（每天凌晨2点备份）
crontab -e
# 添加: 0 2 * * * /www/backup/backup-mengtu.sh
```

**代码备份**
```bash
# 创建代码备份脚本
cat > /www/backup/backup-code.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /www/backup/mengtu-code_\$DATE.tar.gz /www/wwwroot/mengtu-*
find /www/backup -name "mengtu-code_*.tar.gz" -mtime +30 -delete
EOF
```

## API接口文档概览

### 认证相关接口

#### 用户认证
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",    // 邮箱或手机号
  "password": "password123"
}

Response:
{
  "success": true,
  "code": 0,
  "data": {
    "token": "jwt-token",
    "user": {
      "id": 1,
      "username": "user",
      "email": "<EMAIL>",
      "user_type": "investor"
    }
  },
  "message": "登录成功"
}
```

#### 管理员认证
```http
POST /api/auth/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}

Response:
{
  "success": true,
  "code": 0,
  "data": {
    "token": "admin-jwt-token",
    "user": {
      "id": 1,
      "username": "admin",
      "real_name": "管理员"
    }
  },
  "message": "登录成功"
}
```

### 用户管理接口

#### 获取用户列表
```http
GET /api/admin/users?page=1&limit=10&search=keyword
Authorization: Bearer admin-jwt-token

Response:
{
  "success": true,
  "data": {
    "users": [...],
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

#### 更新用户状态
```http
PUT /api/admin/users/:id/status
Authorization: Bearer admin-jwt-token
Content-Type: application/json

{
  "status": "active" // active | inactive
}
```

### 基金管理接口

#### 获取基金列表
```http
GET /api/funds?page=1&limit=10&status=active

Response:
{
  "success": true,
  "data": {
    "funds": [
      {
        "id": 1,
        "name": "基金名称",
        "description": "基金描述",
        "target_amount": 1000000,
        "current_amount": 500000,
        "status": "active"
      }
    ],
    "total": 50
  }
}
```

#### 创建基金
```http
POST /api/admin/funds
Authorization: Bearer admin-jwt-token
Content-Type: application/json

{
  "name": "新基金",
  "description": "基金描述",
  "target_amount": 1000000,
  "min_investment": 10000,
  "expected_return": 15.5
}
```

### 文件上传接口

#### 上传文件
```http
POST /api/admin/upload
Authorization: Bearer admin-jwt-token
Content-Type: multipart/form-data

FormData:
- file: [文件]
- type: "image" | "document"

Response:
{
  "success": true,
  "data": {
    "url": "https://cdn.example.com/file.jpg",
    "filename": "file.jpg",
    "size": 1024000
  }
}
```

### 系统设置接口

#### 获取系统设置
```http
GET /api/public/settings

Response:
{
  "success": true,
  "data": {
    "website": {
      "title": "剧投投",
      "logo": "logo-url",
      "description": "平台描述"
    },
    "contact": {
      "email": "<EMAIL>",
      "phone": "************"
    }
  }
}
```

#### 更新系统设置
```http
PUT /api/admin/settings
Authorization: Bearer admin-jwt-token
Content-Type: application/json

{
  "website": {
    "title": "新标题",
    "logo": "new-logo-url"
  }
}
```

### 响应格式规范

**成功响应**
```json
{
  "success": true,
  "code": 0,
  "data": { ... },
  "message": "操作成功"
}
```

**错误响应**
```json
{
  "success": false,
  "code": 400,
  "message": "错误信息",
  "details": "详细错误描述"
}
```

**分页响应**
```json
{
  "success": true,
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

## 数据库设计说明

### 核心表结构

#### 1. 用户相关表

**users - C端用户表**
```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL,
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(20) UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  user_type ENUM('investor', 'producer', 'fund_manager') DEFAULT 'investor',
  real_name VARCHAR(100),
  company_name VARCHAR(200),
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**admins - 管理员表**
```sql
CREATE TABLE admins (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  real_name VARCHAR(100),
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 权限管理表

**admin_roles - 角色表**
```sql
CREATE TABLE admin_roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  code VARCHAR(50) NOT NULL UNIQUE,
  description TEXT,
  status TINYINT DEFAULT 1
);
```

**admin_menus - 菜单表**
```sql
CREATE TABLE admin_menus (
  id INT PRIMARY KEY,
  pid INT DEFAULT NULL,
  name VARCHAR(100) NOT NULL,
  path VARCHAR(255),
  component VARCHAR(255),
  type ENUM('catalog', 'menu', 'button') NOT NULL,
  status TINYINT DEFAULT 1,
  auth_code VARCHAR(100),
  sort_order INT DEFAULT 0
);
```

#### 3. 业务核心表

**funds - 基金表**
```sql
CREATE TABLE funds (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(200) NOT NULL,
  description TEXT,
  target_amount DECIMAL(15,2) NOT NULL,
  current_amount DECIMAL(15,2) DEFAULT 0,
  min_investment DECIMAL(15,2) DEFAULT 1000,
  expected_return DECIMAL(5,2),
  status ENUM('draft', 'active', 'closed', 'completed') DEFAULT 'draft',
  start_date DATE,
  end_date DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**investments - 投资记录表**
```sql
CREATE TABLE investments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  fund_id INT NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
  invested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (fund_id) REFERENCES funds(id)
);
```

#### 4. 内容管理表

**banners - 轮播图表**
```sql
CREATE TABLE banners (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(200) NOT NULL,
  image_url VARCHAR(500) NOT NULL,
  link_url VARCHAR(500),
  sort_order INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**system_settings - 系统设置表**
```sql
CREATE TABLE system_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(64) NOT NULL UNIQUE,
  setting_value TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 数据库关系图

```
users (C端用户)
  ├── investments (投资记录)
  └── user_profiles (用户资料)

admins (管理员)
  ├── admin_role_relations (角色关联)
  └── audit_logs (操作日志)

admin_roles (角色)
  ├── admin_role_relations (角色关联)
  └── admin_role_permissions (权限关联)

funds (基金)
  ├── investments (投资记录)
  ├── fund_timelines (时间线)
  └── fund_usage_plans (资金使用计划)

banners (轮播图)
actors (演员)
dramas (短剧)
system_settings (系统设置)
audit_logs (审计日志)
```

### 索引设计

**主要索引**
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_type_status ON users(user_type, status);

-- 投资记录索引
CREATE INDEX idx_investments_user_fund ON investments(user_id, fund_id);
CREATE INDEX idx_investments_status ON investments(status);
CREATE INDEX idx_investments_date ON investments(invested_at);

-- 基金表索引
CREATE INDEX idx_funds_status ON funds(status);
CREATE INDEX idx_funds_dates ON funds(start_date, end_date);

-- 审计日志索引
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id, user_type);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_date ON audit_logs(created_at);
```

## 开发规范和最佳实践

### 代码规范

#### 1. TypeScript规范
```typescript
// ✅ 正确：使用明确的类型定义
interface User {
  id: number;
  username: string;
  email: string;
  user_type: 'investor' | 'producer' | 'fund_manager';
}

// ❌ 错误：使用any类型
const user: any = getUserData();

// ✅ 正确：使用泛型
function apiRequest<T>(url: string): Promise<ApiResponse<T>> {
  return fetch(url).then(res => res.json());
}
```

#### 2. Vue组件规范
```vue
<!-- ✅ 正确：使用Composition API -->
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { User } from '@/types/user';

const users = ref<User[]>([]);
const loading = ref(false);

const activeUsers = computed(() =>
  users.value.filter(user => user.status === 1)
);

onMounted(async () => {
  await loadUsers();
});
</script>

<!-- ✅ 正确：使用scoped样式 -->
<style scoped>
.user-card {
  @apply bg-white rounded-lg shadow-md p-4;
}
</style>
```

#### 3. API设计规范
```typescript
// ✅ 正确：统一的响应格式
interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  data?: T;
  message: string;
  details?: string;
}

// ✅ 正确：RESTful API设计
// GET /api/users - 获取用户列表
// GET /api/users/:id - 获取单个用户
// POST /api/users - 创建用户
// PUT /api/users/:id - 更新用户
// DELETE /api/users/:id - 删除用户
```

### 文件组织规范

#### 1. 目录结构
```
src/
├── api/                    # API调用
│   ├── modules/           # 按模块分组
│   │   ├── user.ts
│   │   ├── fund.ts
│   │   └── admin.ts
│   └── index.ts           # 统一导出
├── components/            # 组件
│   ├── common/           # 通用组件
│   ├── business/         # 业务组件
│   └── layout/           # 布局组件
├── composables/          # 组合式函数
├── stores/               # 状态管理
├── types/                # 类型定义
├── utils/                # 工具函数
└── views/                # 页面组件
```

#### 2. 命名规范
```typescript
// 文件命名：kebab-case
user-management.vue
api-client.ts
form-validator.ts

// 组件命名：PascalCase
UserManagement.vue
DataTable.vue
FormInput.vue

// 变量命名：camelCase
const userName = 'admin';
const isLoading = false;
const userList = [];

// 常量命名：SCREAMING_SNAKE_CASE
const API_BASE_URL = 'http://localhost:3001/api';
const MAX_FILE_SIZE = 10 * 1024 * 1024;
```

### Git工作流规范

#### 1. 分支管理
```bash
# 主分支
main                    # 生产环境代码
develop                 # 开发环境代码

# 功能分支
feature/user-management # 功能开发
feature/fund-system     # 功能开发

# 修复分支
hotfix/login-bug        # 紧急修复
bugfix/form-validation  # 问题修复
```

#### 2. 提交信息规范
```bash
# 格式：<type>(<scope>): <description>

# 功能开发
feat(user): add user registration functionality
feat(fund): implement fund investment feature

# 问题修复
fix(auth): resolve login token expiration issue
fix(ui): correct responsive layout on mobile

# 文档更新
docs(api): update API documentation
docs(readme): add deployment instructions

# 代码重构
refactor(components): optimize table component performance
refactor(api): simplify error handling logic
```

### 测试规范

#### 1. 单元测试
```typescript
// 测试文件命名：*.test.ts 或 *.spec.ts
import { describe, it, expect } from 'vitest';
import { validateEmail } from '@/utils/validation';

describe('validateEmail', () => {
  it('should return true for valid email', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
  });

  it('should return false for invalid email', () => {
    expect(validateEmail('invalid-email')).toBe(false);
  });
});
```

#### 2. API测试
```typescript
// API测试示例
describe('User API', () => {
  it('should create user successfully', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    };

    const response = await createUser(userData);

    expect(response.success).toBe(true);
    expect(response.data.user.email).toBe(userData.email);
  });
});
```

### 性能优化规范

#### 1. 前端优化
```typescript
// ✅ 正确：使用懒加载
const UserManagement = defineAsyncComponent(() =>
  import('@/views/UserManagement.vue')
);

// ✅ 正确：使用计算属性缓存
const expensiveValue = computed(() => {
  return heavyCalculation(props.data);
});

// ✅ 正确：使用防抖
import { debounce } from 'lodash-es';

const searchUsers = debounce(async (keyword: string) => {
  await fetchUsers({ search: keyword });
}, 300);
```

#### 2. 后端优化
```typescript
// ✅ 正确：使用数据库索引
// 在查询频繁的字段上添加索引
CREATE INDEX idx_users_email ON users(email);

// ✅ 正确：使用分页查询
export async function getUsers(page: number, limit: number) {
  const offset = (page - 1) * limit;
  return await db.query(`
    SELECT * FROM users
    ORDER BY created_at DESC
    LIMIT ${limit} OFFSET ${offset}
  `);
}

// ✅ 正确：使用缓存
import { LRUCache } from 'lru-cache';

const cache = new LRUCache<string, any>({
  max: 500,
  ttl: 1000 * 60 * 5 // 5分钟
});
```

### 安全规范

#### 1. 认证和授权
```typescript
// ✅ 正确：JWT令牌验证
export async function verifyToken(token: string) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return { success: true, user: decoded };
  } catch (error) {
    return { success: false, error: 'Invalid token' };
  }
}

// ✅ 正确：权限检查
export function hasPermission(user: User, permission: string) {
  return user.permissions.includes(permission);
}
```

#### 2. 数据验证
```typescript
// ✅ 正确：输入验证
import Joi from 'joi';

const userSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required()
});

export function validateUserInput(data: any) {
  return userSchema.validate(data);
}
```

## 故障排除指南

### 常见问题及解决方案

#### 1. 数据库连接问题

**问题症状**
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```

**解决步骤**
```bash
# 1. 检查MySQL服务状态
systemctl status mysql

# 2. 检查端口是否开放
netstat -tlnp | grep 3306

# 3. 检查数据库配置
mysql -u root -p -e "SHOW VARIABLES LIKE 'bind_address';"

# 4. 检查防火墙设置
ufw status
iptables -L

# 5. 验证数据库连接
mysql -h ********* -P 3306 -u root -p
```

#### 2. JWT令牌问题

**问题症状**
```
Error: JsonWebTokenError: invalid signature
```

**解决步骤**
```bash
# 1. 检查JWT密钥配置
echo $JWT_SECRET
echo $JWT_ADMIN_SECRET

# 2. 验证环境变量
cat backend/.env | grep JWT

# 3. 检查令牌格式
# 在浏览器开发者工具中查看Authorization头

# 4. 重新生成密钥
openssl rand -base64 32
```

#### 3. 文件上传问题

**问题症状**
```
Error: File upload failed - Invalid file type
```

**解决步骤**
```bash
# 1. 检查文件大小限制
echo $UPLOAD_MAX_SIZE

# 2. 检查文件类型配置
echo $UPLOAD_ALLOWED_TYPES

# 3. 检查存储空间
df -h

# 4. 检查目录权限
ls -la /www/wwwroot/uploads/
chmod 755 /www/wwwroot/uploads/
```

#### 4. 前端构建问题

**问题症状**
```
Error: Cannot resolve module '@/components/UserTable'
```

**解决步骤**
```bash
# 1. 检查文件是否存在
ls -la src/components/UserTable.vue

# 2. 检查路径别名配置
cat vite.config.ts | grep alias

# 3. 清除缓存重新构建
rm -rf node_modules/.vite
rm -rf dist
pnpm install
pnpm build

# 4. 检查TypeScript配置
cat tsconfig.json | grep paths
```

#### 5. API请求失败

**问题症状**
```
Error: Network Error / ERR_FAILED
```

**解决步骤**
```bash
# 1. 检查后端服务状态
pm2 status
curl http://localhost:3001/api/health

# 2. 检查CORS配置
# 在backend/nitro.config.ts中检查cors设置

# 3. 检查API地址配置
cat website/.env.development | grep VITE_API

# 4. 检查网络连接
ping localhost
telnet localhost 3001
```

### 性能问题排查

#### 1. 数据库性能问题

**诊断命令**
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看进程列表
SHOW PROCESSLIST;

-- 分析查询性能
EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';

-- 查看索引使用情况
SHOW INDEX FROM users;
```

**优化建议**
```sql
-- 添加必要索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);

-- 优化查询语句
-- ❌ 避免全表扫描
SELECT * FROM users WHERE YEAR(created_at) = 2024;

-- ✅ 使用索引友好的查询
SELECT * FROM users WHERE created_at >= '2024-01-01' AND created_at < '2025-01-01';
```

#### 2. 前端性能问题

**诊断工具**
- Chrome DevTools Performance面板
- Lighthouse性能评估
- Vue DevTools

**优化策略**
```typescript
// 1. 组件懒加载
const LazyComponent = defineAsyncComponent(() =>
  import('./HeavyComponent.vue')
);

// 2. 虚拟滚动（大列表）
import { VirtualList } from '@tanstack/vue-virtual';

// 3. 图片懒加载
<img v-lazy="imageUrl" alt="description" />

// 4. 防抖和节流
import { debounce } from 'lodash-es';
const debouncedSearch = debounce(search, 300);
```

### 日志分析

#### 1. 后端日志
```bash
# PM2日志
pm2 logs mengtu-api --lines 100

# 系统日志
tail -f /var/log/syslog | grep mengtu

# Nginx日志
tail -f /www/wwwlogs/your-domain.com.log
tail -f /www/wwwlogs/your-domain.com.error.log
```

#### 2. 前端错误监控
```typescript
// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  // 发送错误报告到监控服务
});

// Vue错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue error:', err, info);
  // 发送错误报告
};
```

### 紧急恢复流程

#### 1. 服务宕机恢复
```bash
# 1. 检查服务状态
pm2 status

# 2. 重启服务
pm2 restart mengtu-api

# 3. 如果重启失败，查看日志
pm2 logs mengtu-api --err

# 4. 从备份恢复
pm2 stop mengtu-api
cp -r /www/backup/mengtu-api-latest/* /www/wwwroot/mengtu-api/
pm2 start mengtu-api
```

#### 2. 数据库恢复
```bash
# 1. 停止应用服务
pm2 stop mengtu-api

# 2. 备份当前数据库
mysqldump -u root -p mengtu > /www/backup/emergency_backup_$(date +%Y%m%d_%H%M%S).sql

# 3. 从备份恢复
mysql -u root -p mengtu < /www/backup/mengtu_latest.sql

# 4. 重启服务
pm2 start mengtu-api
```

---

## 总结

本文档提供了剧投投募资管理系统的完整技术指南，涵盖了项目架构、部署流程、开发规范和故障排除等各个方面。建议开发团队严格按照本指南进行开发和维护工作，确保系统的稳定性和可维护性。

**文档维护**
- 本文档应随着项目的发展持续更新
- 建议每月审查一次文档内容的准确性
- 新功能开发时应同步更新相关文档

**联系方式**
如有技术问题或文档改进建议，请联系开发团队。
