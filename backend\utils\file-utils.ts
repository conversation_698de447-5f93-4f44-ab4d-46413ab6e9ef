/**
 * 文件处理工具函数
 */
import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';
import { logger } from './logger';

/**
 * 生成唯一文件名
 */
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const randomStr = crypto.randomBytes(8).toString('hex');
  const ext = path.extname(originalName);
  const baseName = path.basename(originalName, ext);
  
  // 保留原始文件名的部分，并添加时间戳和随机字符串确保唯一性
  return `${baseName}_${timestamp}_${randomStr}${ext}`;
}

/**
 * 确保目录存在
 */
export async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await fs.access(dirPath);
  } catch (error) {
    // 目录不存在，创建它
    await fs.mkdir(dirPath, { recursive: true });
    logger.info(`创建目录: ${dirPath}`);
  }
}

/**
 * 获取上传文件的完整路径
 */
export async function getUploadPath(filename: string, subdir = ''): Promise<string> {
  // 基础上传目录
  const uploadDir = path.join(process.cwd(), 'uploads');
  
  // 如果有子目录，将其添加到路径
  const targetDir = subdir ? path.join(uploadDir, subdir) : uploadDir;
  
  // 确保目录存在
  await ensureDirectoryExists(targetDir);
  
  // 返回完整的文件路径
  return path.join(targetDir, filename);
}

/**
 * 删除文件
 */
export async function deleteFile(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    await fs.unlink(filePath);
    logger.info(`成功删除文件: ${filePath}`);
    return true;
  } catch (error) {
    if (error.code === 'ENOENT') {
      logger.warn(`要删除的文件不存在: ${filePath}`);
    } else {
      logger.error(`删除文件失败: ${filePath}`, { error: error.message });
    }
    return false;
  }
}

/**
 * 获取文件大小
 */
export async function getFileSize(filePath: string): Promise<number | null> {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    logger.error(`获取文件大小失败: ${filePath}`, { error: error.message });
    return null;
  }
}

/**
 * 检查文件是否存在
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return path.extname(filename).toLowerCase();
}

/**
 * 验证文件类型
 */
export function validateFileType(filename: string, allowedTypes: string[]): boolean {
  const ext = getFileExtension(filename);
  return allowedTypes.includes(ext);
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 生成文件哈希值
 */
export async function generateFileHash(filePath: string, algorithm = 'md5'): Promise<string | null> {
  try {
    const fileBuffer = await fs.readFile(filePath);
    const hash = crypto.createHash(algorithm);
    hash.update(fileBuffer);
    return hash.digest('hex');
  } catch (error) {
    logger.error(`生成文件哈希失败: ${filePath}`, { error: error.message });
    return null;
  }
}

/**
 * 复制文件
 */
export async function copyFile(sourcePath: string, destPath: string): Promise<boolean> {
  try {
    // 确保目标目录存在
    const destDir = path.dirname(destPath);
    await ensureDirectoryExists(destDir);
    
    await fs.copyFile(sourcePath, destPath);
    logger.info(`文件复制成功: ${sourcePath} -> ${destPath}`);
    return true;
  } catch (error) {
    logger.error(`文件复制失败: ${sourcePath} -> ${destPath}`, { error: error.message });
    return false;
  }
}

/**
 * 移动文件
 */
export async function moveFile(sourcePath: string, destPath: string): Promise<boolean> {
  try {
    // 确保目标目录存在
    const destDir = path.dirname(destPath);
    await ensureDirectoryExists(destDir);
    
    await fs.rename(sourcePath, destPath);
    logger.info(`文件移动成功: ${sourcePath} -> ${destPath}`);
    return true;
  } catch (error) {
    logger.error(`文件移动失败: ${sourcePath} -> ${destPath}`, { error: error.message });
    return false;
  }
}

/**
 * 清理临时文件
 */
export async function cleanupTempFiles(tempDir: string, maxAge = 24 * 60 * 60 * 1000): Promise<void> {
  try {
    const files = await fs.readdir(tempDir);
    const now = Date.now();
    
    for (const file of files) {
      const filePath = path.join(tempDir, file);
      const stats = await fs.stat(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        await deleteFile(filePath);
        logger.info(`清理过期临时文件: ${filePath}`);
      }
    }
  } catch (error) {
    logger.error(`清理临时文件失败: ${tempDir}`, { error: error.message });
  }
}
