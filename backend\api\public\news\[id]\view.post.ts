import { query, transaction } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';
import { verifyToken } from '~/utils/auth';

/**
 * 增加新闻阅读量接口
 * POST /api/public/news/:id/view
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取新闻ID
    const newsId = getRouterParam(event, 'id');
    
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    const newsIdNum = parseInt(newsId);

    // 获取客户端信息
    const clientIP = getClientIP(event);
    const userAgent = getHeader(event, 'user-agent') || '';
    
    // 获取用户ID（如果已登录）
    let userId = null;
    try {
      const authHeader = getHeader(event, 'authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = verifyToken(token);
        userId = decoded.userId;
      }
    } catch (error) {
      // 忽略token验证错误，支持匿名阅读
    }

    // 检查新闻是否存在且已发布
    const newsCheckQuery = `
      SELECT id, view_count 
      FROM news 
      WHERE id = ? 
        AND status = 'published' 
        AND publish_date <= NOW()
    `;

    const newsResult = await query(newsCheckQuery, [newsIdNum]);

    if (!newsResult || newsResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '新闻不存在或已下线'
      });
    }

    const currentViewCount = newsResult[0].view_count || 0;

    // 防重复阅读检查（可选）
    // 检查同一IP在短时间内是否已经阅读过
    const recentReadQuery = `
      SELECT id 
      FROM news_read_logs 
      WHERE news_id = ? 
        AND ip_address = ? 
        AND read_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
      LIMIT 1
    `;

    const recentRead = await query(recentReadQuery, [newsIdNum, clientIP]);

    // 如果1小时内已经阅读过，不重复增加阅读量，但仍然返回成功
    if (recentRead && recentRead.length > 0) {
      return {
        success: true,
        message: '阅读量已记录',
        data: {
          newsId: newsIdNum,
          viewCount: currentViewCount,
          incremented: false
        }
      };
    }

    // 使用事务确保数据一致性
    await transaction(async (connection) => {
      // 增加阅读量
      await connection.execute(
        'UPDATE news SET view_count = view_count + 1 WHERE id = ?',
        [newsIdNum]
      );

      // 记录阅读日志
      await connection.execute(
        `INSERT INTO news_read_logs (news_id, user_id, ip_address, user_agent, read_at) 
         VALUES (?, ?, ?, ?, NOW())`,
        [newsIdNum, userId, clientIP, userAgent]
      );
    });

    // 获取更新后的阅读量
    const updatedNewsQuery = `
      SELECT view_count 
      FROM news 
      WHERE id = ?
    `;

    const updatedResult = await query(updatedNewsQuery, [newsIdNum]);
    const newViewCount = updatedResult[0]?.view_count || currentViewCount + 1;

    return {
      success: true,
      message: '阅读量增加成功',
      data: {
        newsId: newsIdNum,
        viewCount: newViewCount,
        incremented: true
      }
    };

  } catch (error: any) {
    logger.error('增加新闻阅读量失败', {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, 'id'),
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '增加阅读量失败'
    });
  }
});
