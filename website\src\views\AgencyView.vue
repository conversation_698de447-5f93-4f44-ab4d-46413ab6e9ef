<script setup>
import { ref, onMounted } from 'vue'
import { getPublicActors } from '../api/actors'
import { getPublicBrands } from '../api/brandService'

// 演员数据
const actors = ref([])
const visibleActors = ref([])
const showAllActors = ref(false)

// 获取演员数据
const loadActors = async () => {
  try {
    const response = await getPublicActors()
    if (response.data.success) {
      actors.value = response.data.data.map(actor => {
        // 安全地解析JSON字符串，添加默认值和非空检查
        let specialty = [];
        let experience = [];
        let description = '';
        let gender = '';
        let age = '';
        let height = '';
        let weight = '';
        
        try {
          // 解析标签 - 不使用JSON.parse，直接使用字符串分割
          if (actor.tags) {
            specialty = actor.tags.split(',').map(tag => tag.trim());
          }
          
          // 解析简介
          if (actor.bio) {
            let bioObj = {};
            if (typeof actor.bio === 'string') {
              try {
                bioObj = JSON.parse(actor.bio);
              } catch (e) {
                console.error('JSON解析失败:', e);
                bioObj = { description: actor.bio };
              }
            } else if (typeof actor.bio === 'object') {
              bioObj = actor.bio;
            }
            
            experience = Array.isArray(bioObj.experience) ? bioObj.experience : [];
            description = bioObj.description || '';
            gender = bioObj.gender || '';
            age = bioObj.age || '';
            height = bioObj.height || '';
            weight = bioObj.weight || '';
          }
        } catch (e) {
          console.error('解析演员数据失败:', e);
        }
        
        return {
          ...actor,
          specialty,
          experience,
          description,
          gender,
          age,
          height,
          weight
        };
      })
      
      // 默认只显示前10个艺人(2行5列)
      updateVisibleActors()
    }
  } catch (error) {
    console.error('获取演员数据失败', error)
  }
}

// 更新可见艺人列表
const updateVisibleActors = () => {
  if (showAllActors.value) {
    visibleActors.value = actors.value
  } else {
    visibleActors.value = actors.value.slice(0, 10) // 显示前10个(2行5列)
  }
}

// 切换显示所有艺人
const toggleShowAllActors = () => {
  showAllActors.value = !showAllActors.value
  updateVisibleActors()
}

// 页面加载时获取数据
onMounted(() => {
  loadActors()
  fetchBrands()
})

// 服务项目
const services = ref([
  {
    title: '艺人经纪',
    icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z',
    description: '为签约艺人提供全方位的经纪服务，包括演艺资源开发、演出安排、行程规划、形象包装等。'
  },
  {
    title: '内容制作',
    icon: 'M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z',
    description: '策划制作符合艺人特质的内容作品，包括网剧、短视频、音乐作品、直播等多种形式。'
  },
  {
    title: '商业合作',
    icon: 'M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
    description: '开发和执行艺人的商业价值，包括品牌代言、商业演出、直播带货、粉丝经济等。'
  },
  {
    title: '培训提升',
    icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
    description: '为艺人提供专业的培训课程，包括表演技巧、镜头表现、台词训练、形体训练等。'
  }
])

// 合作品牌
const brands = ref([])
const brandsLoading = ref(false)
const brandsError = ref('')

// 获取厂牌数据
const fetchBrands = async () => {
  try {
    brandsLoading.value = true
    brandsError.value = ''
    const response = await getPublicBrands({ limit: 12 })
    if (response.data && response.data.success) {
      brands.value = response.data.data
    } else {
      brandsError.value = '获取厂牌数据失败'
    }
  } catch (error) {
    console.error('获取厂牌数据失败:', error)
    brandsError.value = '获取厂牌数据失败'
    // 降级处理：使用默认数据
    brands.value = [
      { id: 1, name: '爱奇艺', logo: '/images/brands/iqiyi.png', companyName: '爱奇艺' },
      { id: 2, name: '腾讯视频', logo: '/images/brands/tencent.png', companyName: '腾讯' },
      { id: 3, name: '优酷', logo: '/images/brands/youku.png', companyName: '优酷' },
      { id: 4, name: '抖音', logo: '/images/brands/douyin.png', companyName: '字节跳动' },
      { id: 5, name: '快手', logo: '/images/brands/kuaishou.png', companyName: '快手' },
      { id: 6, name: '华为', logo: '/images/brands/huawei.png', companyName: '华为' }
    ]
  } finally {
    brandsLoading.value = false
  }
}

// 当前选中的演员
const selectedActor = ref(null)

// 联系合作弹窗状态
const showContactModal = ref(false)

// 查看演员详情
const viewActorDetail = (actor) => {
  selectedActor.value = actor
}

// 关闭演员详情
const closeActorDetail = () => {
  selectedActor.value = null
}

// 处理图片加载错误
const handleImageError = (event) => {
  const img = event.target
  img.style.display = 'none'
  // 可以在这里添加默认图片或图标
}

// 品牌悬停效果
const onBrandHover = () => {
  // 可以在这里添加额外的悬停效果
}
</script>

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <section class="bg-gray-50 py-4">
      <div class="container mx-auto px-4">
        <div class="bg-gradient-primary text-white py-12 text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-4">演艺经纪</h1>
          <p class="text-xl max-w-3xl mx-auto opacity-90">
            剧投投拥有专业的演艺经纪团队，致力于发掘和培养有潜力的影视人才，
            为创作优质内容提供坚实的人才基础
          </p>
        </div>
      </div>
    </section>
    
    <!-- 我们的服务 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">我们的服务</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            为艺人提供全方位的经纪管理与职业发展规划
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="service in services"
            :key="service.title"
            class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300"
          >
            <div class="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center text-white mb-6">
              <svg class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="service.icon" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">{{ service.title }}</h3>
            <p class="text-gray-600">{{ service.description }}</p>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 艺人展示 -->
    <section v-if="actors.length > 0" class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">签约艺人</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            我们拥有多位优秀签约艺人，活跃于各大影视剧、综艺和商业活动
          </p>
        </div>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6">
          <div
            v-for="actor in visibleActors"
            :key="actor.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
          >
            <!-- 演员头像 -->
            <div class="h-64 bg-gray-200 flex items-center justify-center">
              <img 
                v-if="actor.avatar_url" 
                :src="actor.avatar_url" 
                :alt="actor.name"
                class="w-full h-full object-cover"
              >
              <svg 
                v-else
                class="w-24 h-24 text-gray-400" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            
            <div class="p-4">
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-lg font-bold">{{ actor.name }}</h3>
                <div class="text-xs text-gray-500">{{ actor.gender }} | {{ actor.age }}岁</div>
              </div>
              
              <div class="mb-3">
                <div class="text-xs text-gray-600 mb-1">代表作品：</div>
                <p class="text-primary font-medium text-sm truncate">{{ actor.experience && actor.experience.length > 0 ? actor.experience[0] : '暂无' }}</p>
              </div>
              
              <div class="flex flex-wrap gap-1 mb-3">
                <span 
                  v-for="(skill, index) in actor.specialty" 
                  :key="index"
                  class="px-2 py-1 bg-blue-50 text-primary text-xs rounded-full"
                >
                  {{ skill }}
                </span>
              </div>
              
              <button
                @click="viewActorDetail(actor)"
                class="w-full py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors text-sm"
              >
                查看详情
              </button>
            </div>
          </div>
        </div>
        
        <div v-if="actors.length > 10" class="mt-8 text-center">
          <button 
            @click="toggleShowAllActors" 
            class="inline-flex items-center px-6 py-3 border border-primary text-primary bg-white hover:bg-primary hover:text-white rounded-lg transition-colors"
          >
            <span>{{ showAllActors ? '收起' : '更多艺人' }}</span>
            <svg 
              class="w-5 h-5 ml-2" 
              :class="{'transform rotate-180': showAllActors}"
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>
    </section>
    
    <!-- 合作品牌 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gradient">合作品牌</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            我们的艺人与多家知名品牌建立了长期稳定的合作关系
          </p>
        </div>

        <!-- 加载状态 -->
        <div v-if="brandsLoading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p class="mt-4 text-gray-600">正在加载合作品牌...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="brandsError" class="text-center py-12">
          <div class="text-red-500 mb-4">{{ brandsError }}</div>
          <button @click="fetchBrands" class="btn bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors">
            重新加载
          </button>
        </div>

        <!-- 品牌列表 -->
        <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
          <div
            v-for="(brand, index) in brands"
            :key="brand.id"
            class="brand-card bg-white rounded-lg shadow-sm p-6 flex flex-col items-center justify-center h-32 hover:shadow-lg transition-all duration-300 cursor-pointer group"
            :style="{ animationDelay: `${index * 100}ms` }"
            @mouseenter="onBrandHover"
          >
            <!-- 品牌Logo -->
            <div v-if="brand.logo" class="brand-logo-container mb-2 overflow-hidden rounded-lg">
              <img
                :src="brand.logo"
                :alt="brand.name"
                class="brand-logo w-12 h-12 object-contain group-hover:scale-110 transition-transform duration-300"
                @error="handleImageError"
              />
            </div>
            <div v-else class="brand-logo-placeholder w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mb-2">
              <svg class="w-6 h-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>

            <!-- 品牌名称 -->
            <div class="text-sm font-medium text-gray-700 text-center group-hover:text-primary transition-colors duration-300">
              {{ brand.name }}
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!brandsLoading && !brandsError && brands.length === 0" class="text-center py-12">
          <div class="text-gray-500">暂无合作品牌</div>
        </div>
      </div>
    </section>
    
    <!-- 联系合作 -->
    <section class="py-16 bg-gradient-primary text-white">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-6">寻找新星 · 合作共赢</h2>
        <p class="text-xl mb-8 max-w-3xl mx-auto">
          如果您是有潜力的演艺新人或寻求艺人合作的品牌方，欢迎与我们联系
        </p>
        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <button @click="showContactModal = true" class="btn bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-lg transition-colors">
            艺人报名
          </button>
          <button @click="showContactModal = true" class="btn bg-transparent border-2 border-white hover:bg-white/10 px-8 py-3 rounded-lg transition-colors">
            商务合作
          </button>
        </div>
      </div>
    </section>
    
    <!-- 演员详情弹窗 -->
    <div
      v-if="selectedActor"
      class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 p-4"
      @click="closeActorDetail"
    >
      <div
        class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        @click.stop
      >
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-bold">{{ selectedActor.name }}</h3>
            <button
              @click="closeActorDetail"
              class="text-gray-500 hover:text-gray-700"
            >
              <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <!-- 演员头像 -->
              <div class="h-80 bg-gray-200 flex items-center justify-center rounded-lg">
                <img 
                  v-if="selectedActor.avatar_url" 
                  :src="selectedActor.avatar_url" 
                  :alt="selectedActor.name"
                  class="w-full h-full object-cover rounded-lg"
                >
                <svg 
                  v-else
                  class="w-24 h-24 text-gray-400" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            
            <div>
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <div class="text-sm text-gray-500 mb-1">性别</div>
                  <div class="font-medium">{{ selectedActor.gender }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">年龄</div>
                  <div class="font-medium">{{ selectedActor.age }}岁</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">身高</div>
                  <div class="font-medium">{{ selectedActor.height }}cm</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500 mb-1">体重</div>
                  <div class="font-medium">{{ selectedActor.weight }}kg</div>
                </div>
              </div>
              
              <div class="mb-6">
                <div class="text-sm text-gray-500 mb-2">特长</div>
                <div class="flex flex-wrap gap-2">
                  <span
                    v-for="(skill, index) in selectedActor.specialty"
                    :key="index"
                    class="px-3 py-1 bg-blue-50 text-primary text-sm rounded-full"
                  >
                    {{ skill }}
                  </span>
                </div>
              </div>
              
              <div class="mb-6">
                <div class="text-sm text-gray-500 mb-2">代表作品</div>
                <ul class="space-y-2">
                  <li
                    v-for="(exp, index) in selectedActor.experience"
                    :key="index"
                    class="flex items-start"
                  >
                    <span class="text-primary mr-2">•</span>
                    <span>{{ exp }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div class="mt-6">
            <div class="text-sm text-gray-500 mb-2">艺人简介</div>
            <p class="text-gray-700">{{ selectedActor.description }}</p>
          </div>
          
          <div class="mt-8 flex justify-center">
            <button class="btn bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-colors">
              联系经纪人
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系合作弹窗 -->
    <div v-if="showContactModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showContactModal = false">
      <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4" @click.stop>
        <div class="text-center">
          <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-2">联系我们</h3>
          <p class="text-gray-600 mb-4">专业的演艺经纪服务，为您提供一对一咨询</p>

          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="text-sm text-gray-600 mb-2">扫码添加微信咨询</div>
            <div class="w-32 h-32 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
              <svg class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
              </svg>
            </div>
          </div>

          <button @click="showContactModal = false" class="w-full py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-gradient-primary {
  background: linear-gradient(45deg, var(--color-purple-gradient-start) 50%, var(--color-purple-gradient-end) 100%);
}

.text-gradient {
  background: linear-gradient(to right, var(--color-purple-gradient-start), var(--color-purple-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* 品牌卡片动画效果 */
.brand-card {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease-out forwards;
  position: relative;
  overflow: hidden;
}

.brand-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.brand-card:hover::before {
  left: 100%;
}

.brand-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.brand-logo-container {
  transition: all 0.3s ease;
}

.brand-logo {
  transition: transform 0.3s ease;
  filter: grayscale(0.3);
}

.brand-card:hover .brand-logo {
  filter: grayscale(0);
}

/* 淡入上升动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .brand-card {
    height: 120px;
  }

  .brand-logo {
    width: 40px;
    height: 40px;
  }
}
</style>