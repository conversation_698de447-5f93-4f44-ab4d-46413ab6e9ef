<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, RouterLink } from 'vue-router'
import { getPublicFundByCode, getPublicFunds } from '../../api/public/fundApi'
import { formatExpectedReturnToDescription, getReturnDescriptionColor } from '../../utils/fundUtils'

const route = useRoute()
const fundCode = route.params.id

// 状态管理
const loading = ref(true)
const error = ref(null)
const fund = ref(null)
const similarFunds = ref([])
// 常见问题展开状态管理
const expandedFaqs = ref({})
// 弹窗状态管理
const showPurchaseModal = ref(false)
const showContactModal = ref(false)

// 当前时间轴阶段 (1: 募集期, 2: 封闭期, 3: 开放赎回)
const currentTimelineStage = computed(() => {
  if (!fund.value) return 0
  
  const now = new Date()
  
  // 转换日期字符串为日期对象
  const recruitEnd = fund.value.timelines?.find(t => t.stage === '募集期')?.date 
    ? new Date(fund.value.timelines.find(t => t.stage === '募集期').date) 
    : new Date(fund.value.establishDate || '2024-03-31')
    
  const lockEnd = fund.value.timelines?.find(t => t.stage === '封闭期')?.date
    ? new Date(fund.value.timelines.find(t => t.stage === '封闭期').date)
    : new Date(fund.value.exitDate || '2026-03-31')
  
  if (now <= recruitEnd) return 1 // 募集期
  if (now <= lockEnd) return 2    // 封闭期
  return 3                        // 开放赎回
})

// 格式化日期字符串
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return isNaN(date.getTime()) ? dateStr : date.toISOString().split('T')[0]
}

// 基金时间轴数据
const fundTimeline = computed(() => {
  if (!fund.value) {
    return {
      recruitStart: '2024-01-01',
      recruitEnd: '2024-03-31',
      lockStart: '2024-04-01',
      lockEnd: '2026-03-31',
      openStart: '2026-04-01'
    }
  }
  
  // 使用基金数据中的时间线信息，如果没有则使用默认值
  const recruitPeriod = fund.value.timelines?.find(t => t.stage === '募集期')
  const lockPeriod = fund.value.timelines?.find(t => t.stage === '封闭期')
  const openPeriod = fund.value.timelines?.find(t => t.stage === '开放赎回')
  
  // 如果没有时间线数据，则使用建立日期和退出日期
  const establishDate = formatDate(fund.value.establishDate || '2024-01-01')
  const exitDate = formatDate(fund.value.exitDate || '2026-03-31')
  
  // 计算锁定期结束后的日期
  const lockEndDate = new Date(exitDate)
  const openStartDate = new Date(lockEndDate)
  openStartDate.setDate(lockEndDate.getDate() + 1)
  const openStart = formatDate(openStartDate.toISOString())
  
  // 计算募集期结束后的日期
  const recruitEndDate = new Date(establishDate)
  recruitEndDate.setMonth(recruitEndDate.getMonth() + 3)
  const recruitEnd = formatDate(recruitEndDate.toISOString())
  
  // 计算锁定期开始日期
  const lockStartDate = new Date(recruitEnd)
  lockStartDate.setDate(lockStartDate.getDate() + 1)
  const lockStart = formatDate(lockStartDate.toISOString())
  
  return {
    recruitStart: recruitPeriod?.date?.split(' ')[0] || establishDate,
    recruitEnd: recruitEnd,
    lockStart: lockPeriod?.date?.split(' ')[0] || lockStart,
    lockEnd: lockPeriod?.date?.split(' ')[2] || exitDate,
    openStart: openPeriod?.date?.split(' ')[0] || openStart
  }
})

// 风险等级对应的颜色
const riskLevelColors = {
  'R1': 'bg-green-500',
  'R2': 'bg-blue-500',
  'R3': 'bg-yellow-500',
  'R4': 'bg-orange-500',
  'R5': 'bg-red-500',
}

// 基金类型对应的文本
const fundTypeLabels = {
  'equity': '股权型',
  'debt': '债权型',
  'mixed': '混合型',
}

// 封闭期对应的文本
const periodLabels = {
  'short': '≤1年',
  'medium': '1-3年',
  'long': '>3年',
}

// 格式化金额
const formatCurrency = (amount) => {
  if (!amount) return '0'
  if (amount >= 10000000) {
    return `${(amount / 10000000).toFixed(2)}亿`
  } else if (amount >= 10000) {
    return `${(amount / 10000).toFixed(0)}万`
  } else {
    return `${amount.toFixed(0)}`
  }
}

// 计算募集进度百分比
const calculateProgress = (current, target) => {
  if (!current || !target) return 0
  return Math.min(Math.round((current / target) * 100), 100)
}

// 活跃的标签页
const activeTab = ref('overview')

// 切换常见问题展开/折叠状态
const toggleFaq = (index) => {
  expandedFaqs.value[index] = !expandedFaqs.value[index]
}

// 加载基金详情
const loadFundDetails = async () => {
  loading.value = true
  error.value = null

  try {
    console.log('正在加载基金详情，基金代码:', fundCode)
    const response = await getPublicFundByCode(fundCode)

    // 调试完整响应
    console.log('API响应完整数据:', response)
    console.log('API响应data字段:', response.data)

    // 检查响应格式
    if (!response.data) {
      throw new Error('API响应数据为空')
    }

    if (!response.data.success) {
      throw new Error(response.data.message || 'API调用失败')
    }

    fund.value = response.data.data

    // 调试数据
    console.log('前端接收到的基金数据:', fund.value)
    if (fund.value) {
      console.log('前端接收到的资金使用计划数据:', fund.value.usagePlans)
      console.log('前端接收到的资金使用计划描述:', fund.value.usagePlanDescription)
      console.log('前端接收到的成功案例数据:', fund.value.successCases)
    }
    
    // 加载相似基金数据
    try {
      const similarFundsResponse = await getPublicFunds({
        pageSize: 4
      })

      if (similarFundsResponse.data && similarFundsResponse.data.success) {
        const allFunds = similarFundsResponse.data.data.funds || []
        similarFunds.value = allFunds
          .filter(f => f.code !== fundCode) // 排除当前基金
          .slice(0, 2) // 只取前2个
          .map(fund => ({
            id: fund.code,
            title: fund.title,
            risk: fund.risk,
            expectedReturn: formatExpectedReturnToDescription(fund.expectedReturn),
            minInvestment: fund.minInvestment,
          }))
      } else {
        // 降级处理：使用默认数据
        similarFunds.value = [
          {
            id: 'WL-2024-IP-002',
            title: '剧投投IP孵化基金',
            risk: 'R4',
            expectedReturn: '较高',
            minInvestment: 1000000,
          },
          {
            id: 'WL-2024-DRAMA-005',
            title: '剧投投精品短剧基金',
            risk: 'R3',
            expectedReturn: '较高',
            minInvestment: 1000000,
          },
        ]
      }
    } catch (similarErr) {
      console.error('加载相似基金失败:', similarErr)
      // 使用默认数据
      similarFunds.value = [
        {
          id: 'WL-2024-IP-002',
          title: '剧投投IP孵化基金',
          risk: 'R4',
          expectedReturn: '较高',
          minInvestment: 1000000,
        },
        {
          id: 'WL-2024-DRAMA-005',
          title: '剧投投精品短剧基金',
          risk: 'R3',
          expectedReturn: '较高',
          minInvestment: 1000000,
        },
      ]
    }
  } catch (err) {
    console.error('加载基金详情失败:', err)
    error.value = '加载基金详情失败，请刷新页面重试'
  } finally {
    loading.value = false
  }
}

// 下载文档
const downloadDocument = (fileDoc) => {
  try {
    // 使用Blob和URL.createObjectURL强制文件下载而不是打开
    fetch(fileDoc.url || fileDoc.fileUrl)
      .then(response => response.blob())
      .then(blob => {
        // 创建一个临时的URL对象
        const blobUrl = window.URL.createObjectURL(blob);
        
        // 创建一个隐藏的a标签用于下载
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = blobUrl;
        
        // 使用文档原始名称作为下载文件名
        link.download = fileDoc.name;
        
        // 将链接添加到文档中并模拟点击
        document.body.appendChild(link);
        link.click();
        
        // 延迟清理DOM和释放URL对象
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(blobUrl);
        }, 100);
      })
      .catch(error => {
        console.error('下载文件失败:', error);
        alert('下载文件失败，请稍后重试');
      });
  } catch (error) {
    console.error('下载文件失败:', error);
    alert('下载文件失败，请稍后重试');
  }
}

// 显示认购提示弹窗
const showPurchaseNotice = () => {
  showPurchaseModal.value = true
}

// 显示联系弹窗
const showContactInfo = () => {
  showContactModal.value = true
}

// 收藏基金
const toggleFavorite = () => {
  // 在实际环境中，这里应该处理收藏/取消收藏功能
  console.log('收藏/取消收藏基金:', fund.value.title)
  alert(`${fund.value.isFavorite ? '取消收藏' : '收藏'}成功`)
  fund.value.isFavorite = !fund.value.isFavorite
}

// 计算超额收益
const calculateExcessReturn = (perf) => {
  if (!perf.return || !perf.average) return '--';
  
  // 去除百分号并转换为数字
  const returnValue = parseFloat(perf.return.replace('%', ''));
  const averageValue = parseFloat(perf.average.replace('%', ''));
  
  if (isNaN(returnValue) || isNaN(averageValue)) return '--';
  
  // 计算差值
  const excess = (returnValue - averageValue).toFixed(1);
  
  // 添加正负号和百分号
  return excess > 0 ? `+${excess}%` : `${excess}%`;
}

// 确定超额收益的颜色样式
const calculateExcessClass = (perf) => {
  if (!perf.return || !perf.average) return 'text-gray-500';
  
  const returnValue = parseFloat(perf.return.replace('%', ''));
  const averageValue = parseFloat(perf.average.replace('%', ''));
  
  if (isNaN(returnValue) || isNaN(averageValue)) return 'text-gray-500';
  
  return returnValue >= averageValue ? 'text-green-600' : 'text-red-600';
}

// 初始化
onMounted(() => {
  loadFundDetails()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <!-- 返回和面包屑导航 -->
      <div class="flex items-center text-sm text-gray-600 mb-6">
        <RouterLink to="/funds" class="hover:text-primary">旅文基金</RouterLink>
        <svg class="w-3 h-3 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <RouterLink to="/funds/recruit" class="hover:text-primary">基金招募</RouterLink>
        <svg class="w-3 h-3 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <span class="text-gray-900 font-medium">{{ loading ? '加载中...' : (fund ? fund.title : '基金详情') }}</span>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="bg-white rounded-xl shadow-md p-12 mb-8 flex justify-center">
        <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
      </div>
      
      <!-- 错误信息 -->
      <div v-else-if="error" class="bg-white rounded-xl shadow-md p-8 mb-8 text-center">
        <svg class="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p class="text-xl font-medium text-gray-900 mb-2">出错了</p>
        <p class="text-gray-600">{{ error }}</p>
        <button @click="loadFundDetails" class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors">
          重试
        </button>
      </div>
      
      <!-- 基金详情内容 -->
      <template v-else-if="fund">
        <!-- 基金基本信息卡片 -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div class="p-6 border-b">
            <div class="flex justify-between items-center mb-4">
              <div>
                <h1 class="text-2xl md:text-3xl font-bold mb-2">{{ fund.title }}</h1>
                <div class="text-gray-500">{{ fund.code }}</div>
              </div>
              <div class="flex space-x-3">
                <div :class="[riskLevelColors[fund.risk], 'text-white px-3 py-1 rounded-md font-medium']">
                  {{ fund.risk }}级风险
                </div>
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-md font-medium">
                  {{ fundTypeLabels[fund.type] }}
                </div>
              </div>
            </div>
            
            <p class="text-gray-700 mb-6">{{ fund.description }}</p>
            
            <!-- 募集进度条 -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <div class="text-gray-700 font-medium">募集进度</div>
                <div class="text-gray-700 font-medium">
                  {{ calculateProgress(fund.raisedAmount, fund.targetSize) }}%
                </div>
              </div>
              <div class="h-3 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  class="h-full rounded-full bg-primary" 
                  :style="`width: ${calculateProgress(fund.raisedAmount, fund.targetSize)}%`"
                ></div>
              </div>
              <div class="flex justify-between text-sm text-gray-600 mt-2">
                <span>已募集: {{ formatCurrency(fund.raisedAmount) }}</span>
                <span>目标规模: {{ formatCurrency(fund.targetSize) }}</span>
              </div>
            </div>
            
            <!-- 基金关键指标 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="text-gray-600 text-sm mb-1">预期年化收益</div>
                <div class="text-xl font-bold" :class="getReturnDescriptionColor(formatExpectedReturnToDescription(fund.expectedReturn))">
                  {{ formatExpectedReturnToDescription(fund.expectedReturn) }}
                </div>
              </div>
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="text-gray-600 text-sm mb-1">最低起投金额</div>
                <div class="text-xl font-bold">{{ formatCurrency(fund.minInvestment) }}元</div>
              </div>
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="text-gray-600 text-sm mb-1">最低持有期</div>
                <div class="text-xl font-bold">{{ fund.minHoldingPeriod }}个月</div>
              </div>
              <div class="p-4 bg-gray-50 rounded-lg">
                <div class="text-gray-600 text-sm mb-1">封闭期</div>
                <div class="text-xl font-bold">{{ periodLabels[fund.period] }}</div>
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex border-t">
            <button
              @click="showPurchaseNotice"
              class="flex-1 py-4 text-center bg-primary hover:bg-primary-dark text-white font-medium transition-colors"
            >
              立即认购
            </button>
            <div class="w-px bg-gray-200"></div>
            <button
              @click="showContactInfo"
              class="flex-1 py-4 text-center text-primary font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
            >
              <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              立即联系
            </button>
            <div class="w-px bg-gray-200"></div>
            <button 
              @click="toggleFavorite"
              class="flex-1 py-4 text-center font-medium hover:bg-gray-50 transition-colors flex items-center justify-center"
              :class="fund.isFavorite ? 'text-yellow-500' : 'text-gray-600'"
            >
              <svg class="w-5 h-5 mr-2" :fill="fund.isFavorite ? 'currentColor' : 'none'" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
              {{ fund.isFavorite ? '已收藏' : '收藏' }}
            </button>
          </div>
        </div>
        
        <!-- 详细信息标签页 -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <!-- 标签导航 -->
          <div class="flex border-b overflow-x-auto">
            <button 
              v-for="tab in ['overview', 'structure', 'performance', 'documents', 'faq']" 
              :key="tab"
              @click="activeTab = tab"
              class="px-6 py-4 text-gray-700 font-medium whitespace-nowrap transition-colors"
              :class="activeTab === tab ? 'text-primary border-b-2 border-primary' : 'hover:text-primary'"
            >
              {{ 
                tab === 'overview' ? '概览' : 
                tab === 'structure' ? '基金结构' : 
                tab === 'performance' ? '业绩表现' : 
                tab === 'documents' ? '基金文档' : 
                '常见问题'
              }}
            </button>
          </div>
          
          <!-- 标签内容 -->
          <div class="p-6">
            <!-- 基金概览 -->
            <div v-if="activeTab === 'overview'" class="space-y-8">
              <!-- 基金亮点 -->
              <div>
                <h2 class="text-xl font-bold mb-4">基金亮点</h2>
                <div class="space-y-4">
                  <div
                    v-for="(highlight, index) in fund.highlights"
                    :key="index"
                    class="flex items-start"
                  >
                    <div class="flex-shrink-0">
                      <svg class="w-5 h-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <p class="text-gray-700">{{ highlight.content || highlight }}</p>
                    </div>
                  </div>
                  <!-- 如果没有基金亮点数据，显示默认内容 -->
                  <template v-if="!fund.highlights || fund.highlights.length === 0">
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <p class="text-gray-700">专注ESG主题影视内容投资，符合国家政策导向</p>
                      </div>
                    </div>
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <p class="text-gray-700">专业团队管理，丰富的影视投资经验</p>
                      </div>
                    </div>
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <svg class="w-5 h-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                        </svg>
                      </div>
                      <div class="ml-3">
                        <p class="text-gray-700">多元化投资组合，有效分散风险</p>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              
              <!-- 投资策略 -->
              <div>
                <h2 class="text-xl font-bold mb-4">投资策略</h2>
                <div class="bg-gray-50 rounded-lg p-4">
                  <p class="text-gray-700">{{ fund.investmentStrategy }}</p>
                </div>
              </div>
              
              <!-- 投资时间轴 -->
              <div>
                <h2 class="text-xl font-bold mb-4">投资时间轴</h2>
                <div class="bg-white">
                  <div class="relative">
                    <div class="absolute left-[19px] top-8 bottom-8 w-[2px] bg-gray-200"></div>
                    
                    <!-- 募集期 -->
                    <div class="flex items-start mb-8 relative">
                      <div class="flex-shrink-0 rounded-full w-10 h-10 flex items-center justify-center text-sm z-10"
                           :class="currentTimelineStage >= 1 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700'">
                        1
                      </div>
                      <div class="ml-4">
                        <h3 class="font-bold">募集期</h3>
                        <p class="text-gray-600">{{ fundTimeline.recruitStart }} 至 {{ fundTimeline.recruitEnd }}</p>
                      </div>
                    </div>
                    
                    <!-- 封闭期 -->
                    <div class="flex items-start mb-8 relative">
                      <div class="flex-shrink-0 rounded-full w-10 h-10 flex items-center justify-center text-sm z-10"
                           :class="currentTimelineStage >= 2 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700'">
                        2
                      </div>
                      <div class="ml-4">
                        <h3 class="font-bold">封闭期</h3>
                        <p class="text-gray-600">{{ fundTimeline.lockStart }} 至 {{ fundTimeline.lockEnd }}</p>
                      </div>
                    </div>
                    
                    <!-- 开放赎回 -->
                    <div class="flex items-start relative">
                      <div class="flex-shrink-0 rounded-full w-10 h-10 flex items-center justify-center text-sm z-10"
                           :class="currentTimelineStage >= 3 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700'">
                        3
                      </div>
                      <div class="ml-4">
                        <h3 class="font-bold">开放赎回</h3>
                        <p class="text-gray-600">{{ fundTimeline.openStart }} 起</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 风险提示 -->
              <div>
                <h2 class="text-xl font-bold mb-4">风险提示</h2>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-red-800">
                  <p>{{ fund.riskDescription || '市场风险、政策风险、制作风险等多因素影响投资收益' }}</p>
                  <p class="mt-2">本基金有一定风险，投资需谨慎。过往业绩不代表未来表现，基金管理人不对基金收益做出承诺。请认真阅读风险揭示书。</p>
                </div>
              </div>
            </div>
            
            <!-- 基金结构 -->
            <div v-else-if="activeTab === 'structure'" class="space-y-8">
              <!-- 基金标题 -->
              <h2 class="text-xl font-bold mb-4">基金结构</h2>
              
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 基本信息 -->
                <div>
                  <h3 class="text-lg font-bold mb-4">基本信息</h3>
                  <div class="border-t border-gray-200">
                    <div class="grid grid-cols-2 border-b border-gray-200">
                      <div class="py-3 px-4 text-gray-600">成立日期</div>
                      <div class="py-3 px-4 font-medium">{{ formatDate(fund.establishDate) || '2024-01-15' }}</div>
                    </div>
                    <div class="grid grid-cols-2 border-b border-gray-200">
                      <div class="py-3 px-4 text-gray-600">到期日期</div>
                      <div class="py-3 px-4 font-medium">{{ formatDate(fund.exitDate) || '2026-01-15' }}</div>
                    </div>
                    <div class="grid grid-cols-2 border-b border-gray-200">
                      <div class="py-3 px-4 text-gray-600">基金管理人</div>
                      <div class="py-3 px-4 font-medium">{{ fund.manager || '剧投投资产管理有限公司' }}</div>
                    </div>
                    <div class="grid grid-cols-2 border-b border-gray-200">
                      <div class="py-3 px-4 text-gray-600">基金托管人</div>
                      <div class="py-3 px-4 font-medium">{{ fund.trustee || '某大型银行资管部' }}</div>
                    </div>
                    <div class="grid grid-cols-2 border-b border-gray-200">
                      <div class="py-3 px-4 text-gray-600">赎回政策</div>
                      <div class="py-3 px-4 font-medium">{{ fund.redemptionPolicy || '封闭期内不可赎回，封闭期后每季度开放一次赎回' }}</div>
                    </div>
                  </div>
                </div>
                
                <!-- 费用结构 -->
                <div>
                  <h3 class="text-lg font-bold mb-4">费用结构</h3>
                  <div class="border-t border-gray-200">
                    <div v-for="(fee, index) in fund.fees" :key="index" class="grid grid-cols-2 border-b border-gray-200">
                      <div class="py-3 px-4 text-gray-600">{{ fee.name }}</div>
                      <div class="py-3 px-4 font-medium">{{ fee.value }}</div>
                    </div>
                    <!-- 如果没有足够的费用数据，显示默认内容 -->
                    <template v-if="!fund.fees || fund.fees.length === 0">
                      <div class="grid grid-cols-2 border-b border-gray-200">
                        <div class="py-3 px-4 text-gray-600">管理费</div>
                        <div class="py-3 px-4 font-medium">2.0%/年</div>
                      </div>
                      <div class="grid grid-cols-2 border-b border-gray-200">
                        <div class="py-3 px-4 text-gray-600">托管费</div>
                        <div class="py-3 px-4 font-medium">0.25%/年</div>
                      </div>
                      <div class="grid grid-cols-2 border-b border-gray-200">
                        <div class="py-3 px-4 text-gray-600">认购费</div>
                        <div class="py-3 px-4 font-medium">1.0%</div>
                      </div>
                      <div class="grid grid-cols-2 border-b border-gray-200">
                        <div class="py-3 px-4 text-gray-600">赎回费</div>
                        <div class="py-3 px-4 font-medium">封闭期后1.0%，逐年递减</div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              
              <!-- 资金使用计划 -->
              <div class="mt-8">
                <h3 class="text-lg font-bold mb-4">资金使用计划</h3>
                
                <div class="mb-4">
                  <p class="text-gray-700">{{ fund.usagePlanDescription || '本基金募集资金主要用于ESG主题影视内容的投资制作，具体资金使用计划如下：' }}</p>
                </div>
                
                <div class="space-y-2">
                  <div v-for="(plan, index) in fund.usagePlans" :key="index" class="grid grid-cols-6">
                    <div class="col-span-1 py-2 font-medium">{{ plan.percentage }}%</div>
                    <div class="col-span-5 py-2 text-gray-700">{{ plan.description }}</div>
                  </div>
                  <!-- 如果没有资金使用计划数据，显示默认内容 -->
                  <template v-if="!fund.usagePlans || fund.usagePlans.length === 0">
                    <div class="grid grid-cols-6">
                      <div class="col-span-1 py-2 font-medium">60%</div>
                      <div class="col-span-5 py-2 text-gray-700">用于ESG主题影视内容的制作投资</div>
                    </div>
                    <div class="grid grid-cols-6">
                      <div class="col-span-1 py-2 font-medium">20%</div>
                      <div class="col-span-5 py-2 text-gray-700">用于内容营销与发行</div>
                    </div>
                    <div class="grid grid-cols-6">
                      <div class="col-span-1 py-2 font-medium">10%</div>
                      <div class="col-span-5 py-2 text-gray-700">用于IP衍生开发</div>
                    </div>
                    <div class="grid grid-cols-6">
                      <div class="col-span-1 py-2 font-medium">10%</div>
                      <div class="col-span-5 py-2 text-gray-700">预留流动资金</div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            
            <!-- 业绩表现 -->
            <div v-else-if="activeTab === 'performance'" class="space-y-8">
              <!-- 历史业绩 -->
              <div>
                <h2 class="text-xl font-bold mb-4">历史业绩</h2>
                <div class="bg-gray-50 rounded-lg overflow-hidden">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-100">
                      <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年度</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">收益率</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同类平均</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">超额收益</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr v-for="perf in (fund.performances || fund.historicalPerformance || [])" :key="perf.year">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {{ perf.year }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-primary font-medium">
                          {{ perf.return }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">
                          {{ perf.average || '--' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium"
                            :class="calculateExcessClass(perf)">
                          {{ calculateExcessReturn(perf) }}
                        </td>
                      </tr>
                      <!-- 如果没有历史业绩数据，显示默认内容 -->
                      <template v-if="!fund.performances && !fund.historicalPerformance || (fund.performances || fund.historicalPerformance || []).length === 0">
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2023</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-primary font-medium">18.5%</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">12.3%</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">+6.2%</td>
                        </tr>
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2022</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-primary font-medium">15.2%</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">10.8%</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">+4.4%</td>
                        </tr>
                        <tr>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2021</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-primary font-medium">22.1%</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">14.5%</td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">+7.6%</td>
                        </tr>
                      </template>
                    </tbody>
                  </table>
                </div>
              </div>
              
              <!-- 成功案例 -->
              <div>
                <h2 class="text-xl font-bold mb-4">成功案例</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div 
                    v-for="(successCase, index) in fund.successCases" 
                    :key="index"
                    class="bg-white rounded-lg shadow-md overflow-hidden"
                  >
                    <div class="bg-gray-100 p-4">
                      <h3 class="text-lg font-bold mb-1">{{ successCase.title }}</h3>
                      <p class="text-gray-700">{{ successCase.description }}</p>
                    </div>
                    <div class="p-4">
                      <div class="grid grid-cols-2 gap-4 mb-3">
                        <div>
                          <div class="text-xs text-gray-500 mb-1">投资回报率</div>
                          <div class="text-xl font-bold text-primary">{{ successCase.returnRate || successCase.return_rate }}</div>
                        </div>
                        <div class="text-right">
                          <div class="text-xs text-gray-500 mb-1">投资金额</div>
                          <div class="text-sm font-medium">{{ successCase.investmentAmount || successCase.investment_amount }}</div>
                          <div class="text-xs text-gray-500 mt-1">回收周期: {{ successCase.recoveryPeriod || successCase.recovery_period }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 如果没有成功案例数据，显示默认内容 -->
                  <template v-if="!fund.successCases || fund.successCases.length === 0">
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                      <div class="bg-gray-100 p-4">
                        <h3 class="text-lg font-bold mb-1">《星河日记》</h3>
                        <p class="text-gray-700">环保主题纪录片，荣获多项国际奖项，投资回报达200%</p>
                      </div>
                      <div class="p-4">
                        <div class="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <div class="text-xs text-gray-500 mb-1">投资回报率</div>
                            <div class="text-xl font-bold text-primary">200%</div>
                          </div>
                          <div class="text-right">
                            <div class="text-xs text-gray-500 mb-1">投资金额</div>
                            <div class="text-sm font-medium">500万元</div>
                            <div class="text-xs text-gray-500 mt-1">回收周期: 16个月</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                      <div class="bg-gray-100 p-4">
                        <h3 class="text-lg font-bold mb-1">《绿色城市》</h3>
                        <p class="text-gray-700">城市环保题材剧集，全网播放量超5亿，投资回报达180%</p>
                      </div>
                      <div class="p-4">
                        <div class="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <div class="text-xs text-gray-500 mb-1">投资回报率</div>
                            <div class="text-xl font-bold text-primary">180%</div>
                          </div>
                          <div class="text-right">
                            <div class="text-xs text-gray-500 mb-1">投资金额</div>
                            <div class="text-sm font-medium">1200万元</div>
                            <div class="text-xs text-gray-500 mt-1">回收周期: 20个月</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                      <div class="bg-gray-100 p-4">
                        <h3 class="text-lg font-bold mb-1">《海洋守护者》</h3>
                        <p class="text-gray-700">海洋环保主题动画片，全球发行至16个国家，投资回报达150%</p>
                      </div>
                      <div class="p-4">
                        <div class="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <div class="text-xs text-gray-500 mb-1">投资回报率</div>
                            <div class="text-xl font-bold text-primary">150%</div>
                          </div>
                          <div class="text-right">
                            <div class="text-xs text-gray-500 mb-1">投资金额</div>
                            <div class="text-sm font-medium">800万元</div>
                            <div class="text-xs text-gray-500 mt-1">回收周期: 24个月</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              
              <!-- 风险提示 -->
              <div>
                <h2 class="text-xl font-bold mb-4">风险提示</h2>
                <div class="bg-gray-50 rounded-lg p-4">
                  <p class="text-gray-700">{{ fund.riskDescription }}</p>
                </div>
              </div>
            </div>
            
            <!-- 基金文档 -->
            <div v-else-if="activeTab === 'documents'" class="space-y-6">
              <h2 class="text-xl font-bold mb-4">基金文档</h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div 
                  v-for="doc in fund.documents" 
                  :key="doc.name"
                  class="border rounded-lg p-4 flex items-center justify-between hover:border-primary transition-colors"
                >
                  <div class="flex items-center">
                    <div class="bg-gray-100 rounded-lg p-2 mr-4">
                      <svg class="w-10 h-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium">{{ doc.name }}</div>
                      <div class="text-sm text-gray-500">{{ doc.type?.toUpperCase() }} · {{ doc.size }}</div>
                    </div>
                  </div>
                  <button 
                    @click="downloadDocument(doc)"
                    class="text-primary hover:text-primary-dark transition-colors focus:outline-none"
                  >
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 常见问题 -->
            <div v-else-if="activeTab === 'faq'" class="space-y-6">
              <h2 class="text-xl font-bold mb-4">常见问题</h2>
              <div class="space-y-4">
                <div 
                  v-for="(item, index) in fund.faqs" 
                  :key="index"
                  class="border rounded-lg overflow-hidden"
                >
                  <div 
                    @click="toggleFaq(index)" 
                    class="p-4 bg-gray-50 font-medium flex justify-between items-center cursor-pointer"
                  >
                    <div>{{ item.question }}</div>
                    <svg 
                      class="w-5 h-5 transition-transform duration-200"
                      :class="expandedFaqs[index] ? 'transform rotate-180' : ''"
                      fill="none" 
                      viewBox="0 0 24 24" 
                      stroke="currentColor"
                    >
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                  <div 
                    v-show="expandedFaqs[index]" 
                    class="p-4 text-gray-700 border-t border-gray-200"
                  >
                    {{ item.answer }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 相似基金推荐 -->
        <div class="mb-8">
          <h2 class="text-xl font-bold mb-6">您可能还感兴趣</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div 
              v-for="similarFund in similarFunds" 
              :key="similarFund.id"
              class="bg-white rounded-lg shadow-sm p-5"
            >
              <h3 class="text-xl font-bold mb-5">{{ similarFund.title }}</h3>
              <div class="flex items-center">
                <div :class="[riskLevelColors[similarFund.risk], 'text-white text-base px-3 py-1 rounded mr-4 font-medium']">
                  {{ similarFund.risk }}
                </div>
                <div class="text-base text-gray-600">
                  预期收益: <span class="font-medium" :class="getReturnDescriptionColor(similarFund.expectedReturn)">{{ similarFund.expectedReturn }}</span>
                </div>
                <div class="text-base text-gray-600 ml-6">
                  起投: <span class="text-gray-900 font-medium">{{ formatCurrency(similarFund.minInvestment) }}元</span>
                </div>
                <div class="ml-auto">
                  <RouterLink 
                    :to="`/funds/${similarFund.id}`" 
                    class="inline-block px-8 py-2 border rounded text-primary border-primary hover:bg-primary hover:text-white focus:bg-primary focus:text-white active:bg-primary active:text-white transition-colors text-base font-medium"
                  >
                    查看
                  </RouterLink>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 认购提示弹窗 -->
      <div v-if="showPurchaseModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showPurchaseModal = false">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4" @click.stop>
          <div class="text-center">
            <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-10 h-10 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">暂不开放认购</h3>
            <p class="text-gray-600 mb-6">剧投投平台暂不对外开放基金认购</p>
            <button @click="showPurchaseModal = false" class="w-full py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
              我知道了
            </button>
          </div>
        </div>
      </div>

      <!-- 联系弹窗 -->
      <div v-if="showContactModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showContactModal = false">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4" @click.stop>
          <div class="text-center">
            <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">联系我们</h3>
            <p class="text-gray-600 mb-4">专业的基金咨询服务，为您提供一对一指导</p>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
              <div class="text-sm text-gray-600 mb-2">扫码添加微信咨询</div>
              <div class="w-32 h-32 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
                <svg class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
              </div>
            </div>

            <button @click="showContactModal = false" class="w-full py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors">
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-primary {
  color: #8667F0;
}
.bg-primary {
  background-color: #8667F0;
}
.border-primary {
  border-color: #8667F0;
}
.text-primary-dark {
  color: #6039E4;
}
.bg-primary-dark {
  background-color: #6039E4;
}
.hover\:bg-primary-dark:hover {
  background-color: #6039E4;
}
.hover\:text-primary-dark:hover {
  color: #6039E4;
}
.border-t-primary {
  border-top-color: #8667F0;
}
</style> 