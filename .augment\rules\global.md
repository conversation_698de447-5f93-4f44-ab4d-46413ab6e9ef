---
type: "always_apply"
description: "剧投投项目全局开发规范 - 综合技术栈和开发流程指导"
---
# 剧投投项目全局开发规范

## 项目架构概述
### 三大核心项目
- **backend/**: Nitro框架后端服务 (TypeScript + MySQL + JWT认证)
- **website/**: Vue3前端官网 (Vue 3 + TypeScript + Tailwind CSS + Pinia)
- **fundAdmin/**: Vue Vben Admin后台管理系统 (企业级管理后台)

## 技术栈规范
### 前端技术栈
- **框架**: Vue 3 + TypeScript (严格模式)
- **样式**: Tailwind CSS (移动优先响应式设计)
- **状态管理**: Pinia (模块化store设计)
- **路由**: Vue Router (懒加载 + 路由守卫)
- **表单验证**: Vee-Validate + Yup schema
- **HTTP请求**: Axios (统一封装 + 拦截器)

### 后端技术栈
- **框架**: <PERSON><PERSON> (热重载 + 类型安全)
- **数据库**: MySQL2 (连接池 + 事务处理)
- **认证**: JWT双token机制 (access_token + refresh_token)
- **文件存储**: 腾讯云COS/阿里云OSS + 本地存储
- **权限管理**: RBAC角色权限控制

## 项目结构规范
### 目录组织原则
- **backend/**: 后端服务根目录
  - api/ - API路由 (/auth, /admin, /users, /public等)
  - utils/ - 工具函数 (数据库、认证、文件处理等)
  - types/ - TypeScript类型定义
  - middleware/ - 中间件 (认证、权限检查等)

- **website/**: 前端网站根目录
  - src/api/ - API调用函数 (按模块组织)
  - src/components/ - 可复用组件
  - src/views/ - 页面组件
  - src/store/ - Pinia状态管理
  - src/types/ - 类型定义

- **fundAdmin/**: 后台管理系统根目录
  - apps/web-antd/ - Ant Design版本
  - src/views/ - 管理页面 (剧目、基金、用户等管理)
  - src/api/ - 后台API调用

### 命名规范
- **组件文件**: PascalCase (UserProfile.vue)
- **工具函数**: camelCase (formatDate.ts)
- **目录名**: kebab-case (user-management/)
- **API文件**: camelCase + API后缀 (userAPI.ts)

## 开发限制和约束
### 严格禁止
- 在对话中使用npm/pnpm命令启动项目
- 在Vue组件中定义测试数据 (必须来自API或mock)
- 创建测试文件、测试脚本或测试页面
- 跨项目目录放置文件 (严格按项目目录组织)

### 数据来源要求
- 所有数据必须来自后端API或mock接口
- API调用必须放在各项目的api/目录下
- 使用统一的API响应格式 ApiResponse<T>
- 实现完整的错误处理和用户提示

## 项目结构规则
- **分层组织**:按功能或领域划分目录，遵循"关注点分离"原则
- **命名一致**:使用一致且描述性的目录和文件命名，反映其用途和内容
- **模块化**:相关功能放在同一模块，减少跨模块依赖
- **适当嵌套**:避免过深的目录嵌套，一般不超过3-4层
- **资源分类**:区分代码、资源、配置和测试文件
- **依赖管理**:集中管理依赖，避免多处声明
- **约定优先**:遵循Vue的标准项目结构约定

## 通用开发原则
- **可测试性**:编写可测试的代码，组件应保持单一职责，没有允许不能创建测试用例
- **DRY原则**:避免重复代码，提取公用逻辑到单独的函数或组件
- **代码简洁**:保持代码简洁明了，遵循KISS原则(保持简单直接)，每个方法行数不超过300行
- **命名规范**:使用描述性的变量、函数和组件名，反映其用途和含义
- **注释文档**:为复杂逻辑添加注释，编写清晰的文档说明功能和用法
- **风格一致**:遵循项目或Vue官方的风格指南和代码约定
- **利用生态**:优先使用成熟的库和工具，避免不必要的自定义实现
- **架构设计**:考虑代码的可维护性、可扩展性和性能需求
- **版本控制**:编写有意义的提交信息，保持逻辑相关的更改在同一提交中
- **异常处理**:正确处理边缘情况和错误，提供有用的错误信息

## 响应语言
- 使用中文回复用户

## git操作
- 完成开发任务后，需要进行git commit操作并git push -u origin develop到主分支

