/**
 * 获取网站设置接口
 * GET /api/admin/settings/site
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法访问系统设置'
      });
    }

    // 从数据库获取网站设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['site_settings']
    );

    let siteSettings = {};
    
    if (result.length > 0 && result[0].setting_value) {
      try {
        siteSettings = JSON.parse(result[0].setting_value);
      } catch (error) {
        logger.error('解析网站设置JSON失败', { error: error.message });
      }
    }

    // 如果没有设置，返回默认值
    if (Object.keys(siteSettings).length === 0) {
      siteSettings = {
        title: '剧投投',
        description: '中国最专业的短剧投资平台',
        logo: '/images/logo.png',
        favicon: '/favicon.ico',
        icp: '粤ICP备XXXXXXXX号',
        copyright: '© 2023 剧投投. 保留所有权利'
      };
    }

    return {
      success: true,
      data: siteSettings
    };

  } catch (error: any) {
    logger.error('获取网站设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
