/**
 * 更新短剧文档接口
 * PUT /api/admin/dramas/[id]/documents/[documentId]
 */
export default defineEventHandler(async (event) => {
  try {
    // 验证管理员权限
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 获取参数
    const dramaId = getRouterParam(event, 'id');
    const documentId = getRouterParam(event, 'documentId');
    
    if (!dramaId || isNaN(Number(dramaId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的短剧ID'
      });
    }

    if (!documentId || isNaN(Number(documentId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的文档ID'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { name, fileUrl, fileType, fileSize } = body;

    // 验证必填字段
    if (!name || name.trim() === '') {
      throw createError({
        statusCode: 400,
        statusMessage: '文档名称不能为空'
      });
    }

    if (!fileUrl || fileUrl.trim() === '') {
      throw createError({
        statusCode: 400,
        statusMessage: '文件链接不能为空'
      });
    }

    // 检查文档是否存在且属于指定短剧
    const existingDocument = await query(
      'SELECT id, drama_id, name, file_url FROM drama_documents WHERE id = ? AND drama_id = ?',
      [documentId, dramaId]
    );

    if (existingDocument.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '短剧文档不存在'
      });
    }

    // 更新短剧文档
    await query(
      `UPDATE drama_documents
       SET name = ?, file_url = ?, file_type = ?, file_size = ?
       WHERE id = ? AND drama_id = ?`,
      [name.trim(), fileUrl.trim(), fileType || null, fileSize || null, documentId, dramaId]
    );

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_UPDATE_DRAMA_DOCUMENT',
      description: `管理员更新短剧文档: 短剧ID=${dramaId}, 文档ID=${documentId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      metadata: {
        dramaId: Number(dramaId),
        documentId: Number(documentId),
        oldName: existingDocument[0].name,
        newName: name.trim(),
        oldFileUrl: existingDocument[0].file_url,
        newFileUrl: fileUrl.trim(),
        fileType: fileType || null,
        fileSize: fileSize || null
      }
    });

    logger.info('短剧文档更新成功', {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      dramaId: Number(dramaId),
      documentId: Number(documentId),
      name: name.trim()
    });

    return {
      success: true,
      message: '短剧文档更新成功',
      data: {
        id: Number(documentId),
        dramaId: Number(dramaId),
        name: name.trim(),
        fileUrl: fileUrl.trim(),
        fileType: fileType || null,
        fileSize: fileSize || null,
        updatedAt: new Date().toISOString()
      }
    };

  } catch (error: any) {
    logger.error('更新短剧文档失败', {
      error: error.message,
      dramaId: getRouterParam(event, 'id'),
      documentId: getRouterParam(event, 'documentId')
    });

    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || '更新短剧文档失败'
    });
  }
});
