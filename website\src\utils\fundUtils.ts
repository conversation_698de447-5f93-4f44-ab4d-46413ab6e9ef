/**
 * 基金相关工具函数
 */

/**
 * 将数字收益率转换为合规的描述性词汇
 * @param expectedReturn 原始收益率字符串（如"15%", "20-25%"等）
 * @returns 合规的描述性词汇
 */
export function formatExpectedReturnToDescription(expectedReturn: string): string {
  if (!expectedReturn || typeof expectedReturn !== 'string') {
    return '适中'
  }

  // 移除百分号和空格
  const cleanReturn = expectedReturn.replace(/%/g, '').trim()
  
  // 处理范围值（如"20-25"）
  if (cleanReturn.includes('-')) {
    const [min, max] = cleanReturn.split('-').map(num => parseFloat(num.trim()))
    if (!isNaN(min) && !isNaN(max)) {
      const avgReturn = (min + max) / 2
      return getReturnDescription(avgReturn)
    }
  }
  
  // 处理单一数值
  const numericReturn = parseFloat(cleanReturn)
  if (!isNaN(numericReturn)) {
    return getReturnDescription(numericReturn)
  }
  
  // 如果已经是描述性词汇，直接返回
  const descriptions = ['较低', '稳健', '适中', '较高', '积极']
  if (descriptions.includes(expectedReturn)) {
    return expectedReturn
  }
  
  return '适中'
}

/**
 * 根据数值收益率返回对应的描述
 * @param returnValue 数值收益率
 * @returns 描述性词汇
 */
function getReturnDescription(returnValue: number): string {
  if (returnValue <= 5) {
    return '较低'
  } else if (returnValue <= 10) {
    return '稳健'
  } else if (returnValue <= 15) {
    return '适中'
  } else if (returnValue <= 25) {
    return '较高'
  } else {
    return '积极'
  }
}

/**
 * 获取收益率描述对应的颜色样式类
 * @param description 收益率描述
 * @returns CSS类名
 */
export function getReturnDescriptionColor(description: string): string {
  const colorMap: Record<string, string> = {
    '较低': 'text-blue-600',
    '稳健': 'text-green-600', 
    '适中': 'text-yellow-600',
    '较高': 'text-orange-600',
    '积极': 'text-red-600'
  }
  
  return colorMap[description] || 'text-gray-600'
}

/**
 * 获取收益率描述的详细说明
 * @param description 收益率描述
 * @returns 详细说明文字
 */
export function getReturnDescriptionDetail(description: string): string {
  const detailMap: Record<string, string> = {
    '较低': '风险较低，收益相对稳定',
    '稳健': '风险适中，追求稳健收益',
    '适中': '平衡风险与收益',
    '较高': '追求较高收益，承担相应风险',
    '积极': '积极投资策略，风险较高'
  }
  
  return detailMap[description] || '平衡风险与收益'
}
