import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取所有新闻标签接口（用于标签选择）
 * GET /api/admin/news/tags/all
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法查看标签列表'
      });
    }

    // 查询所有标签，按使用频率排序
    const tagsQuery = `
      SELECT 
        nt.id,
        nt.name,
        nt.created_at,
        COUNT(ntr.news_id) as usage_count
      FROM news_tags nt
      LEFT JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      GROUP BY nt.id, nt.name, nt.created_at
      ORDER BY usage_count DESC, nt.name ASC
      LIMIT 100
    `;

    const tags = await query(tagsQuery);

    // 格式化标签数据
    const formattedTags = tags.map((tag: any) => ({
      id: tag.id,
      name: tag.name,
      usageCount: tag.usage_count || 0,
      createdAt: tag.created_at
    }));

    return {
      success: true,
      data: formattedTags
    };

  } catch (error: any) {
    logger.error('获取所有新闻标签失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取标签列表失败'
    });
  }
});
