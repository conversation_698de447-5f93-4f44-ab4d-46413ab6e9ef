/**
 * 简单的日志工具
 */

import { getHeader } from 'h3';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

class Logger {
  private level: LogLevel;

  constructor() {
    const config = useRuntimeConfig();
    const logLevel = (config.logLevel || 'info').toLowerCase();
    
    switch (logLevel) {
      case 'debug':
        this.level = LogLevel.DEBUG;
        break;
      case 'info':
        this.level = LogLevel.INFO;
        break;
      case 'warn':
        this.level = LogLevel.WARN;
        break;
      case 'error':
        this.level = LogLevel.ERROR;
        break;
      default:
        this.level = LogLevel.INFO;
    }
  }

  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}`;
  }

  debug(message: string, meta?: any): void {
    if (this.level <= LogLevel.DEBUG) {
      console.log(this.formatMessage('debug', message, meta));
    }
  }

  info(message: string, meta?: any): void {
    if (this.level <= LogLevel.INFO) {
      console.log(this.formatMessage('info', message, meta));
    }
  }

  warn(message: string, meta?: any): void {
    if (this.level <= LogLevel.WARN) {
      console.warn(this.formatMessage('warn', message, meta));
    }
  }

  error(message: string, meta?: any): void {
    if (this.level <= LogLevel.ERROR) {
      console.error(this.formatMessage('error', message, meta));
    }
  }
}

// 导出单例实例
export const logger = new Logger();

// 审计日志接口
export interface AuditLogData {
  userId?: number;
  username?: string;
  userType?: 'admin' | 'user' | 'investor';
  action: string;
  description: string;
  ip?: string;
  userAgent?: string;
  path?: string;
  method?: string;
  statusCode?: number;
  responseTime?: number;
  metadata?: any;
}

/**
 * 记录审计日志
 */
export async function logAuditAction(data: AuditLogData): Promise<void> {
  try {
    const { query } = await import('./database');
    
    await query(
      `INSERT INTO audit_logs 
       (user_id, username, user_type, action, description, ip, user_agent, 
        path, method, status_code, response_time, metadata, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        data.userId || null,
        data.username || null,
        data.userType || null,
        data.action,
        data.description,
        data.ip || null,
        data.userAgent || null,
        data.path || null,
        data.method || null,
        data.statusCode || null,
        data.responseTime || null,
        data.metadata ? JSON.stringify(data.metadata) : null
      ]
    );
    
    logger.info('审计日志已记录', { action: data.action, userId: data.userId });
  } catch (error) {
    logger.error('记录审计日志失败', { error: error.message, data });
  }
}

/**
 * 获取客户端IP地址
 */
export function getClientIP(event: any): string | undefined {
  // 使用H3的getRequestIP函数，如果不可用则回退到手动解析
  try {
    return getRequestIP(event, { xForwardedFor: true }) || undefined;
  } catch {
    // 回退到手动解析
    const headers = event.node?.req?.headers || {};
    return headers['x-forwarded-for']?.split(',')[0]?.trim() ||
           headers['x-real-ip'] ||
           headers['x-client-ip'] ||
           event.node?.req?.connection?.remoteAddress ||
           event.node?.req?.socket?.remoteAddress ||
           undefined;
  }
}

/**
 * 从H3事件中提取审计信息
 */
export function extractAuditInfo(event: any): Partial<AuditLogData> {
  return {
    ip: getClientIP(event) || undefined,
    userAgent: getHeader(event, 'user-agent') || undefined,
    path: event.node?.req?.url || undefined,
    method: event.node?.req?.method || undefined,
  };
}

/**
 * 记录管理员操作日志
 */
export async function logAdminAction(
  adminId: number,
  action: string,
  description: string,
  metadata?: any
): Promise<void> {
  try {
    await logAuditAction({
      userId: adminId,
      userType: 'admin',
      action,
      description,
      metadata
    });
  } catch (error) {
    logger.error('记录管理员操作日志失败', { error: error.message, adminId, action });
  }
}
