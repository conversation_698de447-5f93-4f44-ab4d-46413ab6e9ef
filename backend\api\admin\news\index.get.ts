import { query } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员获取新闻列表接口
 * GET /api/admin/news
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法查看新闻列表'
      });
    }

    // 获取查询参数
    const query_params = getQuery(event);
    const { 
      page = 1, 
      pageSize = 20, 
      status,
      category,
      search,
      author,
      featured,
      startDate,
      endDate,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = query_params;

    // 参数验证
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize as string) || 20));
    const offset = (pageNum - 1) * pageSizeNum;

    // 构建查询条件
    let whereConditions: string[] = [];
    let queryParams: any[] = [];

    // 状态筛选
    if (status && ['draft', 'pending', 'published', 'archived'].includes(status as string)) {
      whereConditions.push('n.status = ?');
      queryParams.push(status);
    }

    // 分类筛选
    if (category && !isNaN(parseInt(category as string))) {
      whereConditions.push('n.category_id = ?');
      queryParams.push(parseInt(category as string));
    }

    // 作者筛选
    if (author && typeof author === 'string' && author.trim()) {
      whereConditions.push('n.author LIKE ?');
      queryParams.push(`%${author.trim()}%`);
    }

    // 推荐筛选
    if (featured === 'true') {
      whereConditions.push('n.is_featured = 1');
    } else if (featured === 'false') {
      whereConditions.push('n.is_featured = 0');
    }

    // 搜索条件
    if (search && typeof search === 'string' && search.trim()) {
      whereConditions.push('(n.title LIKE ? OR n.summary LIKE ? OR n.content LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    // 日期范围筛选
    if (startDate && typeof startDate === 'string') {
      whereConditions.push('DATE(n.created_at) >= ?');
      queryParams.push(startDate);
    }

    if (endDate && typeof endDate === 'string') {
      whereConditions.push('DATE(n.created_at) <= ?');
      queryParams.push(endDate);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 构建排序条件
    const allowedOrderBy = ['created_at', 'updated_at', 'publish_date', 'view_count', 'title', 'status'];
    const orderByField = allowedOrderBy.includes(orderBy as string) ? orderBy : 'created_at';
    const orderDir = orderDirection === 'ASC' ? 'ASC' : 'DESC';

    // 查询新闻列表
    const newsQuery = `
      SELECT 
        n.id,
        n.title,
        n.summary,
        n.cover_image_url,
        n.author,
        n.source_url,
        n.status,
        n.is_featured,
        n.view_count,
        n.publish_date,
        n.created_at,
        n.updated_at,
        nc.id as category_id,
        nc.name as category_name,
        nc.slug as category_slug
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      ${whereClause}
      ORDER BY n.${orderByField} ${orderDir}
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      ${whereClause}
    `;

    // 执行查询
    const [newsList, countResult] = await Promise.all([
      query(newsQuery, queryParams),
      query(countQuery, queryParams)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / pageSizeNum);

    // 格式化新闻数据
    const formattedNews = newsList.map((news: any) => ({
      id: news.id,
      title: news.title,
      summary: news.summary,
      coverImage: news.cover_image_url,
      author: news.author,
      sourceUrl: news.source_url,
      status: news.status,
      isFeatured: news.is_featured === 1,
      viewCount: news.view_count,
      publishDate: news.publish_date,
      createdAt: news.created_at,
      updatedAt: news.updated_at,
      category: news.category_id ? {
        id: news.category_id,
        name: news.category_name,
        slug: news.category_slug
      } : null
    }));

    // 记录审计日志
    await logAdminAction(admin.id, 'news:list', '查看新闻列表', {
      filters: { status, category, search, author, featured },
      pagination: { page: pageNum, pageSize: pageSizeNum }
    });

    return {
      success: true,
      data: {
        list: formattedNews,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };

  } catch (error: any) {
    logger.error('获取新闻列表失败', {
      error: error.message,
      stack: error.stack,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '获取新闻列表失败'
    });
  }
});
