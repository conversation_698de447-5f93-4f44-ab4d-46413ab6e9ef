<script setup>
import { ref, onMounted } from 'vue'
import { RouterLink } from 'vue-router'

// 登录状态
const isLoggedIn = ref(false)
// 资金报告数据
const fundReports = ref([
  {
    id: 1,
    fundName: '剧投投文旅ESG影视基金',
    fundCode: 'WL-2024-ESG-001',
    reportType: '季度报告',
    reportDate: '2024-03-31',
    reportTitle: '2024年第一季度资金使用报告',
    size: '2.5MB',
    description: '详细披露资金投向、项目进展及风险情况',
  },
  {
    id: 2,
    fundName: '剧投投IP孵化基金',
    fundCode: 'WL-2024-IP-002',
    reportType: '季度报告',
    reportDate: '2024-03-31',
    reportTitle: '2024年第一季度资金使用报告',
    size: '2.3MB',
    description: '详细披露资金投向、项目进展及风险情况',
  },
  {
    id: 3,
    fundName: '剧投投精品短剧基金',
    fundCode: 'WL-2024-DRAMA-005',
    reportType: '季度报告',
    reportDate: '2024-03-31',
    reportTitle: '2024年第一季度资金使用报告',
    size: '1.9MB',
    description: '详细披露资金投向、项目进展及风险情况',
  },
  {
    id: 4,
    fundName: '剧投投文旅ESG影视基金',
    fundCode: 'WL-2024-ESG-001',
    reportType: '年度报告',
    reportDate: '2023-12-31',
    reportTitle: '2023年年度资金使用报告',
    size: '6.2MB',
    description: '详细披露全年资金使用情况、投资收益及下一年度展望',
  },
])

// 合规公告
const complianceAnnouncements = ref([
  {
    id: 1,
    date: '2024-04-15',
    title: '关于《剧投投文旅ESG影视基金》托管行变更公告',
    type: '变更公告',
    importance: 'normal',
  },
  {
    id: 2,
    date: '2024-03-20',
    title: '关于《剧投投IP孵化基金》投资范围调整的公告',
    type: '变更公告',
    importance: 'high',
  },
  {
    id: 3,
    date: '2024-02-10',
    title: '剧投投资产管理有限公司关于旗下基金合规运作的年度报告',
    type: '合规报告',
    importance: 'normal',
  },
  {
    id: 4,
    date: '2024-01-15',
    title: '关于旅文基金系列产品2024年投资策略的说明',
    type: '投资策略',
    importance: 'normal',
  },
])

// 投资者名录（仅管理员可见）
const investorsList = ref([
  {
    id: 1,
    fundCode: 'WL-2024-ESG-001',
    totalInvestors: 28,
    totalAmount: 32500000,
    updateDate: '2024-04-10',
  },
  {
    id: 2,
    fundCode: 'WL-2024-IP-002',
    totalInvestors: 21,
    totalAmount: 18000000,
    updateDate: '2024-04-10',
  },
  {
    id: 3,
    fundCode: 'WL-2024-DEBT-003',
    totalInvestors: 16,
    totalAmount: 85000000,
    updateDate: '2024-04-10',
  },
])

// 用户角色
const userRole = ref('investor') // investor, admin

// 模拟登录
const login = () => {
  // 实际项目中应该进行实际的API调用登录
  isLoggedIn.value = true
}

// 活跃的标签页
const activeTab = ref('reports')

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 格式化金额
const formatCurrency = (amount) => {
  if (amount >= 10000000) {
    return `${(amount / 10000000).toFixed(2)}亿`
  } else if (amount >= 10000) {
    return `${(amount / 10000).toFixed(0)}万`
  } else {
    return `${amount.toFixed(0)}`
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="container mx-auto px-4">
      <!-- 页面标题 -->
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 class="text-2xl md:text-3xl font-bold">信息披露</h1>
          <p class="text-gray-600 mt-2">查看基金运作数据与合规报告</p>
        </div>
        <div class="mt-4 md:mt-0">
          <RouterLink to="/funds" class="text-primary flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
            </svg>
            返回基金主页
          </RouterLink>
        </div>
      </div>
      
      <!-- 未登录状态 -->
      <div v-if="!isLoggedIn" class="bg-white rounded-xl shadow-md p-8 text-center">
        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        <h2 class="text-2xl font-bold mb-4">需要登录访问</h2>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          信息披露区域仅对已认证的投资者开放，请登录后查看详细内容
        </p>
        
        <form class="max-w-md mx-auto mb-6">
          <div class="mb-4">
            <label for="username" class="block text-left text-gray-700 mb-2">用户名</label>
            <input 
              type="text" 
              id="username" 
              placeholder="请输入您的用户名" 
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            >
          </div>
          <div class="mb-6">
            <label for="password" class="block text-left text-gray-700 mb-2">密码</label>
            <input 
              type="password" 
              id="password" 
              placeholder="请输入您的密码" 
              class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            >
          </div>
          <button 
            type="button"
            @click="login"
            class="w-full py-3 bg-primary hover:bg-primary-dark text-white font-medium rounded-md transition-colors"
          >
            登录
          </button>
        </form>
        
        <div class="text-sm text-gray-600">
          <p>还没有账户？<RouterLink to="/dashboard" class="text-primary">立即注册</RouterLink></p>
          <p class="mt-2">投资者登录问题请联系：400-123-4567</p>
        </div>
      </div>
      
      <!-- 已登录状态 -->
      <div v-else>
        <!-- 标签页导航 -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
          <div class="flex border-b overflow-x-auto">
            <button 
              v-for="tab in userRole === 'admin' ? ['reports', 'announcements', 'investors'] : ['reports', 'announcements']" 
              :key="tab"
              @click="activeTab = tab"
              class="px-6 py-4 text-gray-700 font-medium whitespace-nowrap transition-colors"
              :class="activeTab === tab ? 'text-primary border-b-2 border-primary' : 'hover:text-primary'"
            >
              {{ 
                tab === 'reports' ? '资金报告' : 
                tab === 'announcements' ? '合规公告' : 
                '投资者名录' 
              }}
            </button>
          </div>
          
          <!-- 标签内容 -->
          <div class="p-6">
            <!-- 资金报告 -->
            <div v-if="activeTab === 'reports'">
              <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <h2 class="text-xl font-bold">资金使用报告</h2>
                
                <!-- 筛选器 -->
                <div class="flex space-x-2 mt-4 md:mt-0">
                  <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                    <option value="">全部基金</option>
                    <option>剧投投文旅ESG影视基金</option>
                    <option>剧投投IP孵化基金</option>
                    <option>剧投投精品短剧基金</option>
                  </select>
                  <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                    <option value="">全部报告类型</option>
                    <option>季度报告</option>
                    <option>年度报告</option>
                  </select>
                </div>
              </div>
              
              <!-- 报告列表 -->
              <div class="space-y-4">
                <div 
                  v-for="report in fundReports" 
                  :key="report.id"
                  class="border rounded-lg p-4 hover:shadow-md transition-shadow duration-300"
                >
                  <div class="flex justify-between items-start mb-3">
                    <div>
                      <h3 class="font-bold text-lg">{{ report.reportTitle }}</h3>
                      <div class="text-sm text-gray-500">{{ report.fundName }} ({{ report.fundCode }})</div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="px-3 py-1 bg-primary bg-opacity-10 text-primary rounded-full text-sm">{{ report.reportType }}</span>
                      <span class="text-gray-500 text-sm">{{ formatDate(report.reportDate) }}</span>
                    </div>
                  </div>
                  <p class="text-gray-700 text-sm mb-4">{{ report.description }}</p>
                  <div class="flex justify-between items-center">
                    <span class="text-gray-500 text-sm">文件大小: {{ report.size }}</span>
                    <button class="flex items-center text-primary hover:text-primary-dark">
                      <svg class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                      </svg>
                      下载报告
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 合规公告 -->
            <div v-if="activeTab === 'announcements'">
              <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold">合规公告</h2>
                
                <!-- 筛选器 -->
                <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary text-sm">
                  <option value="">全部公告类型</option>
                  <option>变更公告</option>
                  <option>合规报告</option>
                  <option>投资策略</option>
                </select>
              </div>
              
              <!-- 公告列表 -->
              <div class="space-y-4">
                <div 
                  v-for="announcement in complianceAnnouncements" 
                  :key="announcement.id"
                  class="border rounded-lg p-4 hover:shadow-md transition-shadow duration-300"
                  :class="{'border-red-300 bg-red-50': announcement.importance === 'high'}"
                >
                  <div class="flex justify-between items-start">
                    <div>
                      <h3 class="font-bold text-lg">{{ announcement.title }}</h3>
                      <div class="flex items-center space-x-2 mt-1">
                        <span class="text-gray-500 text-sm">{{ formatDate(announcement.date) }}</span>
                        <span class="px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full text-xs">{{ announcement.type }}</span>
                        <span v-if="announcement.importance === 'high'" class="px-2 py-0.5 bg-red-100 text-red-700 rounded-full text-xs">重要</span>
                      </div>
                    </div>
                    <button class="text-primary hover:text-primary-dark">
                      <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 投资者名录（仅管理员可见） -->
            <div v-if="activeTab === 'investors' && userRole === 'admin'">
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                  <svg class="w-5 h-5 text-yellow-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p class="text-yellow-700">
                    此区域仅限管理员查看。投资者名录包含敏感信息，请妥善保管并遵守相关隐私法规。
                  </p>
                </div>
              </div>
              
              <h2 class="text-xl font-bold mb-4">投资者汇总信息</h2>
              <div class="overflow-x-auto">
                <table class="min-w-full bg-white rounded-lg overflow-hidden">
                  <thead class="bg-gray-100">
                    <tr>
                      <th class="py-3 px-4 text-left font-medium">基金代码</th>
                      <th class="py-3 px-4 text-left font-medium">投资者数量</th>
                      <th class="py-3 px-4 text-left font-medium">总投资金额</th>
                      <th class="py-3 px-4 text-left font-medium">最近更新</th>
                      <th class="py-3 px-4 text-left font-medium">操作</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y">
                    <tr v-for="investor in investorsList" :key="investor.id">
                      <td class="py-3 px-4 font-medium">{{ investor.fundCode }}</td>
                      <td class="py-3 px-4">{{ investor.totalInvestors }}人</td>
                      <td class="py-3 px-4">{{ formatCurrency(investor.totalAmount) }}元</td>
                      <td class="py-3 px-4">{{ formatDate(investor.updateDate) }}</td>
                      <td class="py-3 px-4">
                        <button class="text-primary hover:text-primary-dark text-sm">查看详情</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 信息安全提示 -->
        <div class="bg-white rounded-xl shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">信息安全提示</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="flex items-start">
              <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <div>
                <h3 class="font-bold mb-1">安全连接</h3>
                <p class="text-sm text-gray-600">
                  所有数据均通过加密通道传输，确保您的信息安全
                </p>
              </div>
            </div>
            
            <div class="flex items-start">
              <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <div>
                <h3 class="font-bold mb-1">保密信息</h3>
                <p class="text-sm text-gray-600">
                  披露信息仅限投资者查看，不得复制或分享给第三方
                </p>
              </div>
            </div>
            
            <div class="flex items-start">
              <svg class="w-5 h-5 text-primary mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 class="font-bold mb-1">退出登录</h3>
                <p class="text-sm text-gray-600">
                  浏览完毕后请及时退出登录，保护您的账户安全
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template> 