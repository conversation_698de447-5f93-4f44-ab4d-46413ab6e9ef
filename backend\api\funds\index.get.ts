/**
 * 获取公开基金列表接口
 * GET /api/funds
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    
    const {
      type,
      risk,
      minInvestment,
      period,
      search
    } = queryParams;

    // 构建查询条件（只查询已发布的基金）
    let whereClause = 'WHERE is_published = 1';
    const params: any[] = [];

    if (type) {
      whereClause += ' AND type = ?';
      params.push(type);
    }

    if (risk) {
      whereClause += ' AND risk = ?';
      params.push(risk);
    }

    if (minInvestment) {
      whereClause += ' AND min_investment >= ?';
      params.push(parseInt(minInvestment as string));
    }

    if (period) {
      whereClause += ' AND period = ?';
      params.push(period);
    }

    if (search) {
      whereClause += ' AND (title LIKE ? OR code LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }

    // 获取总数
    const countResult = await query(
      `SELECT COUNT(*) as total FROM funds ${whereClause}`,
      params
    );
    const total = countResult[0]?.total || 0;

    // 获取基金列表
    const funds = await query(
      `SELECT code, title, description, type, risk, min_investment, period,
              target_size, raised_amount, expected_return, min_holding_period,
              risk_description, created_at
       FROM funds 
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );

    // 格式化基金数据（对外使用code作为id）
    const formattedFunds = funds.map((fund: any) => ({
      id: fund.code,
      title: fund.title,
      description: fund.description,
      type: fund.type,
      risk: fund.risk,
      minInvestment: fund.min_investment,
      period: fund.period,
      targetSize: fund.target_size,
      raisedAmount: fund.raised_amount,
      expectedReturn: fund.expected_return,
      minHoldingPeriod: fund.min_holding_period,
      riskDescription: fund.risk_description,
      createdAt: fund.created_at
    }));

    return {
      success: true,
      data: {
        list: formattedFunds,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };

  } catch (error: any) {
    logger.error('获取公开基金列表失败', {
      error: error.message,
      ip: getClientIP(event)
    });

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
