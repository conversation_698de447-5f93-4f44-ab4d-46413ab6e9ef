import { query, transaction } from '~/utils/database';
import { logger, logAdminAction, getClientIP } from '~/utils/logger';
import { checkAdminPermission, PERMISSIONS } from '~/utils/permission';

/**
 * 管理员更新新闻接口
 * PUT /api/admin/news/:id
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法更新新闻'
      });
    }

    // 获取新闻ID
    const newsId = getRouterParam(event, 'id');
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的新闻ID'
      });
    }

    const newsIdNum = parseInt(newsId);

    // 检查新闻是否存在
    const existingNews = await query(
      'SELECT id, title, status FROM news WHERE id = ?',
      [newsIdNum]
    );

    if (!existingNews || existingNews.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '新闻不存在'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { 
      title, 
      summary, 
      content, 
      cover_image_url, 
      category_id, 
      author, 
      source_url, 
      status,
      is_featured,
      publish_date,
      tags = [],
      seo
    } = body;

    // 验证必填字段
    if (title !== undefined && (!title || typeof title !== 'string' || title.trim().length === 0)) {
      throw createError({
        statusCode: 400,
        statusMessage: '新闻标题不能为空'
      });
    }

    if (content !== undefined && (!content || typeof content !== 'string' || content.trim().length === 0)) {
      throw createError({
        statusCode: 400,
        statusMessage: '新闻内容不能为空'
      });
    }

    // 验证状态值
    if (status !== undefined) {
      const validStatuses = ['draft', 'pending', 'published', 'archived'];
      if (!validStatuses.includes(status)) {
        throw createError({
          statusCode: 400,
          statusMessage: '无效的新闻状态'
        });
      }
    }

    // 验证分类ID（如果提供）
    if (category_id !== undefined && category_id !== null && !isNaN(parseInt(category_id))) {
      const categoryCheck = await query(
        'SELECT id FROM news_categories WHERE id = ? AND is_active = 1',
        [parseInt(category_id)]
      );
      
      if (!categoryCheck || categoryCheck.length === 0) {
        throw createError({
          statusCode: 400,
          statusMessage: '无效的新闻分类'
        });
      }
    }

    // 处理发布时间
    let publishDateTime = undefined;
    if (publish_date !== undefined) {
      if (publish_date === null) {
        publishDateTime = null;
      } else {
        publishDateTime = new Date(publish_date);
        if (isNaN(publishDateTime.getTime())) {
          throw createError({
            statusCode: 400,
            statusMessage: '无效的发布时间'
          });
        }
      }
    }

    // 使用事务更新新闻
    await transaction(async (connection) => {
      // 构建更新字段
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (title !== undefined) {
        updateFields.push('title = ?');
        updateValues.push(title.trim());
      }

      if (summary !== undefined) {
        updateFields.push('summary = ?');
        updateValues.push(summary ? summary.trim() : null);
      }

      if (content !== undefined) {
        updateFields.push('content = ?');
        updateValues.push(content.trim());
      }

      if (cover_image_url !== undefined) {
        updateFields.push('cover_image_url = ?');
        updateValues.push(cover_image_url || null);
      }

      if (category_id !== undefined) {
        updateFields.push('category_id = ?');
        updateValues.push(category_id ? parseInt(category_id) : null);
      }

      if (author !== undefined) {
        updateFields.push('author = ?');
        updateValues.push(author ? author.trim() : null);
      }

      if (source_url !== undefined) {
        updateFields.push('source_url = ?');
        updateValues.push(source_url || null);
      }

      if (status !== undefined) {
        updateFields.push('status = ?');
        updateValues.push(status);
        
        // 如果状态改为已发布且没有发布时间，设置当前时间
        if (status === 'published' && publishDateTime === undefined) {
          updateFields.push('publish_date = ?');
          updateValues.push(new Date());
        }
      }

      if (is_featured !== undefined) {
        updateFields.push('is_featured = ?');
        updateValues.push(is_featured ? 1 : 0);
      }

      if (publishDateTime !== undefined) {
        updateFields.push('publish_date = ?');
        updateValues.push(publishDateTime);
      }

      // 添加更新时间
      updateFields.push('updated_at = NOW()');

      if (updateFields.length > 0) {
        updateValues.push(newsIdNum);
        
        const updateQuery = `
          UPDATE news 
          SET ${updateFields.join(', ')} 
          WHERE id = ?
        `;

        await connection.execute(updateQuery, updateValues);
      }

      // 更新标签关联
      if (tags !== undefined && Array.isArray(tags)) {
        // 删除现有标签关联
        await connection.execute(
          'DELETE FROM news_tag_relations WHERE news_id = ?',
          [newsIdNum]
        );

        // 添加新的标签关联
        for (const tagName of tags) {
          if (typeof tagName === 'string' && tagName.trim()) {
            // 查找或创建标签
            let tagId;
            const existingTag = await connection.execute(
              'SELECT id FROM news_tags WHERE name = ?',
              [tagName.trim()]
            );

            if ((existingTag as any)[0].length > 0) {
              tagId = (existingTag as any)[0][0].id;
            } else {
              const tagResult = await connection.execute(
                'INSERT INTO news_tags (name) VALUES (?)',
                [tagName.trim()]
              );
              tagId = (tagResult as any).insertId;
            }

            // 创建新闻标签关联
            await connection.execute(
              'INSERT INTO news_tag_relations (news_id, tag_id) VALUES (?, ?)',
              [newsIdNum, tagId]
            );
          }
        }
      }

      // 更新SEO信息
      if (seo !== undefined && typeof seo === 'object') {
        const {
          meta_title,
          meta_description,
          meta_keywords,
          canonical_url,
          og_title,
          og_description,
          og_image
        } = seo;

        // 检查是否已有SEO记录
        const existingSeo = await connection.execute(
          'SELECT news_id FROM news_seo WHERE news_id = ?',
          [newsIdNum]
        );

        if ((existingSeo as any)[0].length > 0) {
          // 更新现有SEO记录
          await connection.execute(
            `UPDATE news_seo SET 
              meta_title = ?, meta_description = ?, meta_keywords = ?,
              canonical_url = ?, og_title = ?, og_description = ?, og_image = ?,
              updated_at = NOW()
            WHERE news_id = ?`,
            [
              meta_title || null,
              meta_description || null,
              meta_keywords || null,
              canonical_url || null,
              og_title || null,
              og_description || null,
              og_image || null,
              newsIdNum
            ]
          );
        } else {
          // 创建新的SEO记录
          await connection.execute(
            `INSERT INTO news_seo (
              news_id, meta_title, meta_description, meta_keywords,
              canonical_url, og_title, og_description, og_image
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              newsIdNum,
              meta_title || null,
              meta_description || null,
              meta_keywords || null,
              canonical_url || null,
              og_title || null,
              og_description || null,
              og_image || null
            ]
          );
        }
      }
    });

    // 记录审计日志
    await logAdminAction(admin.id, 'news:update', '更新新闻', {
      newsId: newsIdNum,
      title: title || existingNews[0].title,
      changes: Object.keys(body)
    });

    return {
      success: true,
      message: '新闻更新成功',
      data: {
        id: newsIdNum
      }
    };

  } catch (error: any) {
    logger.error('更新新闻失败', {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, 'id'),
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: '更新新闻失败'
    });
  }
});
