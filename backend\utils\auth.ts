import type { EventHandlerRequest, H3Event } from 'h3';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { query } from './database';
import type { RowDataPacket } from 'mysql2/promise';

// C端用户信息接口
export interface UserInfo {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  avatar?: string;
  status: number;
  created_at?: string;
  updated_at?: string;
  password_hash?: string; // C端用户密码哈希字段
  user_type?: string; // 用户类型
}

// 管理员信息接口
export interface AdminInfo {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  real_name?: string;
  avatar?: string;
  home_path?: string;
  status: number;
  created_at?: string;
  updated_at?: string;
  last_login_at?: string;
  password?: string; // 添加密码字段，用于数据库查询
}

// JWT载荷接口 - 用户
export interface UserJWTPayload extends Omit<UserInfo, 'password'> {
  iat: number;
  exp: number;
  type: 'user'; // 标识令牌类型
}

// JWT载荷接口 - 管理员
export interface AdminJWTPayload extends Omit<AdminInfo, 'password'> {
  iat: number;
  exp: number;
  type: 'admin'; // 标识令牌类型
}

/**
 * 生成用户访问令牌
 */
export function generateUserAccessToken(user: UserInfo): string {
  const config = useRuntimeConfig();
  const payload = {
    id: user.id,
    username: user.username,
    email: user.email,
    avatar: user.avatar,
    status: user.status,
    type: 'user'
  };

  return jwt.sign(payload, config.jwtSecret, { expiresIn: config.jwtExpiresIn });
}

/**
 * 生成管理员访问令牌
 */
export function generateAdminAccessToken(admin: AdminInfo): string {
  const config = useRuntimeConfig();
  const payload = {
    id: admin.id,
    username: admin.username,
    email: admin.email,
    real_name: admin.real_name,
    avatar: admin.avatar,
    home_path: admin.home_path,
    status: admin.status,
    type: 'admin'
  };

  return jwt.sign(payload, config.jwtSecret, { expiresIn: config.jwtAdminExpiresIn });
}

/**
 * 生成用户刷新令牌
 */
export function generateUserRefreshToken(user: UserInfo): string {
  const config = useRuntimeConfig();
  return jwt.sign(
    { userId: user.id, username: user.username, type: 'user' },
    config.refreshTokenSecret || config.jwtSecret,
    { expiresIn: '30d' }
  );
}

/**
 * 生成管理员刷新令牌
 */
export function generateAdminRefreshToken(admin: AdminInfo): string {
  const config = useRuntimeConfig();
  return jwt.sign(
    { userId: admin.id, username: admin.username, type: 'admin' },
    config.refreshTokenSecret || config.jwtSecret,
    { expiresIn: '7d' } // 管理员刷新令牌有效期较短
  );
}

/**
 * 验证用户访问令牌
 */
export function verifyUserAccessToken(
  event: H3Event<EventHandlerRequest>
): UserJWTPayload | null {
  const authHeader = getHeader(event, 'Authorization');
  if (!authHeader?.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.split(' ')[1];
  try {
    const config = useRuntimeConfig();
    const decoded = jwt.verify(token, config.jwtSecret) as UserJWTPayload;

    // 验证令牌类型
    if (decoded.type !== 'user') {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error('User token verification failed:', error);
    return null;
  }
}

/**
 * 验证管理员访问令牌
 */
export function verifyAdminAccessToken(
  event: H3Event<EventHandlerRequest>
): AdminJWTPayload | null {
  const authHeader = getHeader(event, 'Authorization');
  if (!authHeader?.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.split(' ')[1];
  try {
    const config = useRuntimeConfig();
    const decoded = jwt.verify(token, config.jwtSecret) as AdminJWTPayload;

    // 验证令牌类型
    if (decoded.type !== 'admin') {
      return null;
    }

    return decoded;
  } catch (error) {
    console.error('Admin token verification failed:', error);
    return null;
  }
}

/**
 * 验证刷新令牌
 */
export function verifyRefreshToken(token: string): UserJWTPayload | AdminJWTPayload | null {
  try {
    const config = useRuntimeConfig();
    const secret = config.refreshTokenSecret || config.jwtSecret;
    const decoded = jwt.verify(token, secret) as UserJWTPayload | AdminJWTPayload;
    return decoded;
  } catch (error) {
    console.error('Refresh token verification failed:', error);
    return null;
  }
}

/**
 * 通用令牌验证函数
 */
export function verifyToken(token: string): UserJWTPayload | AdminJWTPayload | null {
  try {
    const config = useRuntimeConfig();
    const decoded = jwt.verify(token, config.jwtSecret) as UserJWTPayload | AdminJWTPayload;
    return decoded;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

/**
 * 密码加密
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}

/**
 * 密码验证
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  console.log('密码验证详情:');
  console.log('输入密码长度:', password.length);
  console.log('存储的哈希值:', hash);
  console.log('哈希值格式:', hash.startsWith('$2') ? 'bcrypt格式' : '其他格式');

  try {
    const result = await bcrypt.compare(password, hash);
    console.log('bcrypt.compare结果:', result);
    return result;
  } catch (error) {
    console.error('密码验证出错:', error);
    return false;
  }
}

/**
 * 根据邮箱或手机号查找C端用户（不支持用户名登录）
 */
export async function findUserByCredentials(identifier: string): Promise<UserInfo | null> {
  try {
    console.log('查找用户:', identifier);
    const users = await query<RowDataPacket[]>(
      `SELECT id, username, email, phone, password_hash, status, user_type,
              created_at, updated_at
       FROM users
       WHERE (email = ? OR phone = ?) AND status = 1`,
      [identifier, identifier]
    );

    console.log('数据库查询结果:', users.length > 0 ? '找到用户' : '未找到用户');
    if (users.length > 0) {
      console.log('用户信息:', {
        id: users[0].id,
        username: users[0].username,
        email: users[0].email,
        user_type: users[0].user_type,
        status: users[0].status
      });
    }

    return users.length > 0 ? users[0] as UserInfo : null;
  } catch (error) {
    console.error('Error finding user:', error);
    return null;
  }
}

/**
 * 根据用户名或邮箱查找管理员
 */
export async function findAdminByCredentials(identifier: string): Promise<AdminInfo | null> {
  try {
    const admins = await query<RowDataPacket[]>(
      `SELECT id, username, email, phone, password, real_name, avatar,
              home_path, status, created_at, updated_at, last_login_at
       FROM admins
       WHERE (username = ? OR email = ? OR phone = ?) AND status = 1`,
      [identifier, identifier, identifier]
    );

    return admins.length > 0 ? admins[0] as AdminInfo : null;
  } catch (error) {
    console.error('Error finding admin:', error);
    return null;
  }
}

/**
 * 根据ID查找C端用户
 */
export async function findUserById(id: number): Promise<UserInfo | null> {
  try {
    const users = await query<RowDataPacket[]>(
      `SELECT id, username, email, phone, avatar, status,
              created_at, updated_at
       FROM users
       WHERE id = ? AND status = 1`,
      [id]
    );

    return users.length > 0 ? users[0] as UserInfo : null;
  } catch (error) {
    console.error('Error finding user by ID:', error);
    return null;
  }
}

/**
 * 根据ID查找管理员
 */
export async function findAdminById(id: number): Promise<AdminInfo | null> {
  try {
    const admins = await query<RowDataPacket[]>(
      `SELECT id, username, email, phone, real_name, avatar,
              home_path, status, created_at, updated_at, last_login_at
       FROM admins
       WHERE id = ? AND status = 1`,
      [id]
    );

    return admins.length > 0 ? admins[0] as AdminInfo : null;
  } catch (error) {
    console.error('Error finding admin by ID:', error);
    return null;
  }
}

/**
 * 更新用户最后登录时间
 */
export async function updateUserLastLogin(userId: number): Promise<void> {
  try {
    await query(
      'UPDATE users SET updated_at = NOW() WHERE id = ?',
      [userId]
    );
  } catch (error) {
    console.error('Error updating user last login:', error);
  }
}

/**
 * 更新管理员最后登录时间
 */
export async function updateAdminLastLogin(adminId: number): Promise<void> {
  try {
    await query(
      'UPDATE admins SET last_login_at = NOW(), updated_at = NOW() WHERE id = ?',
      [adminId]
    );
  } catch (error) {
    console.error('Error updating admin last login:', error);
  }
}

/**
 * 检查是否为管理员令牌
 */
export function isAdminToken(payload: UserJWTPayload | AdminJWTPayload): boolean {
  return payload.type === 'admin';
}

/**
 * 检查是否为用户令牌
 */
export function isUserToken(payload: UserJWTPayload | AdminJWTPayload): boolean {
  return payload.type === 'user';
}

/**
 * 检查管理员权限（基于角色）
 */
export async function hasAdminPermission(adminId: number, permission: string): Promise<boolean> {
  try {
    // 查询管理员的角色和权限
    const permissions = await query<RowDataPacket[]>(
      `SELECT arp.permission_code
       FROM admin_role_relations arr
       JOIN admin_role_permissions arp ON arr.role_id = arp.role_id
       WHERE arr.admin_id = ? AND arp.permission_code = ?`,
      [adminId, permission]
    );

    return permissions.length > 0;
  } catch (error) {
    console.error('Error checking admin permission:', error);
    return false;
  }
}

/**
 * 获取管理员所有权限
 */
export async function getAdminPermissions(adminId: number): Promise<string[]> {
  try {
    const permissions = await query<RowDataPacket[]>(
      `SELECT DISTINCT arp.permission_code
       FROM admin_role_relations arr
       JOIN admin_role_permissions arp ON arr.role_id = arp.role_id
       WHERE arr.admin_id = ?`,
      [adminId]
    );

    return permissions.map(p => p.permission_code);
  } catch (error) {
    console.error('Error getting admin permissions:', error);
    return [];
  }
}

// 兼容性函数 - 保持向后兼容
export function generateAccessToken(user: UserInfo): string {
  return generateUserAccessToken(user);
}

export function generateRefreshToken(user: UserInfo): string {
  return generateUserRefreshToken(user);
}

export function verifyAccessToken(event: H3Event<EventHandlerRequest>): UserJWTPayload | null {
  return verifyUserAccessToken(event);
}

export function updateLastLogin(userId: number): Promise<void> {
  return updateUserLastLogin(userId);
}

export function isAdmin(user: any): boolean {
  return user?.is_admin === true || user?.is_admin === 1 || user?.type === 'admin';
}

export function hasPermission(user: any, permission: string): boolean {
  return isAdmin(user);
}
