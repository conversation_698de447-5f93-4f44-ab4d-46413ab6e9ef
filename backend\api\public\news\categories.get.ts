import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';

/**
 * 获取公开新闻分类列表接口
 * GET /api/public/news/categories
 */
export default defineEventHandler(async (event) => {
  try {
    // 查询启用的分类，包含新闻数量统计
    const categoriesQuery = `
      SELECT 
        nc.id,
        nc.name,
        nc.slug,
        nc.description,
        nc.parent_id,
        nc.sort_order,
        COUNT(n.id) as news_count
      FROM news_categories nc
      LEFT JOIN news n ON nc.id = n.category_id 
        AND n.status = 'published' 
        AND n.publish_date <= NOW()
      WHERE nc.is_active = 1
      GROUP BY nc.id, nc.name, nc.slug, nc.description, nc.parent_id, nc.sort_order
      ORDER BY nc.sort_order ASC, nc.id ASC
    `;

    const categories = await query(categoriesQuery);

    // 格式化分类数据
    const formattedCategories = categories.map((category: any) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parentId: category.parent_id,
      sortOrder: category.sort_order,
      newsCount: category.news_count || 0
    }));

    // 构建层级结构（如果有父子分类）
    const categoryMap = new Map();
    const rootCategories: any[] = [];

    // 先创建所有分类的映射
    formattedCategories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 构建层级关系
    formattedCategories.forEach(category => {
      const categoryWithChildren = categoryMap.get(category.id);
      
      if (category.parentId) {
        // 有父分类，添加到父分类的children中
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children.push(categoryWithChildren);
        } else {
          // 父分类不存在或未启用，作为根分类处理
          rootCategories.push(categoryWithChildren);
        }
      } else {
        // 根分类
        rootCategories.push(categoryWithChildren);
      }
    });

    // 获取热门标签（可选）
    const tagsQuery = `
      SELECT 
        nt.id,
        nt.name,
        COUNT(ntr.news_id) as usage_count
      FROM news_tags nt
      INNER JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      INNER JOIN news n ON ntr.news_id = n.id
      WHERE n.status = 'published' 
        AND n.publish_date <= NOW()
      GROUP BY nt.id, nt.name
      HAVING usage_count > 0
      ORDER BY usage_count DESC, nt.name ASC
      LIMIT 20
    `;

    const popularTags = await query(tagsQuery);

    const formattedTags = popularTags.map((tag: any) => ({
      id: tag.id,
      name: tag.name,
      usageCount: tag.usage_count
    }));

    return {
      success: true,
      data: {
        categories: rootCategories,
        flatCategories: formattedCategories, // 平铺的分类列表，方便前端使用
        popularTags: formattedTags
      }
    };

  } catch (error: any) {
    logger.error('获取新闻分类列表失败', {
      error: error.message,
      stack: error.stack,
      ip: getClientIP(event)
    });

    return {
      success: false,
      message: '获取分类列表失败',
      data: {
        categories: [],
        flatCategories: [],
        popularTags: []
      }
    };
  }
});
