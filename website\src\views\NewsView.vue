<script setup>
import { ref, computed, onMounted } from 'vue'
import { useNewsStore } from '@/store/news'

// 使用新闻store
const newsStore = useNewsStore()
const selectedCategory = ref('全部')

// 分类列表
const categories = computed(() => {
  const cats = ['全部']
  if (newsStore.flatCategories.length > 0) {
    cats.push(...newsStore.flatCategories.map(cat => cat.name))
  }
  return cats
})

// 筛选后的新闻列表
const filteredNews = computed(() => {
  if (selectedCategory.value === '全部') {
    return newsStore.newsList
  }
  return newsStore.newsList.filter(news =>
    news.category?.name === selectedCategory.value
  )
})

// 选择分类
const selectCategory = async (category) => {
  selectedCategory.value = category

  if (category === '全部') {
    await newsStore.fetchNewsList()
  } else {
    // 根据分类名称查找对应的slug
    const categoryObj = newsStore.flatCategories.find(cat => cat.name === category)
    if (categoryObj) {
      await newsStore.fetchNewsByCategory(categoryObj.slug)
    }
  }
}

// 订阅表单
const email = ref('')
const isSubmitting = ref(false)
const subscribeSuccess = ref(false)

// 订阅处理
const handleSubscribe = () => {
  if (!email.value) return
  
  isSubmitting.value = true
  
  // 模拟API请求
  setTimeout(() => {
    isSubmitting.value = false
    subscribeSuccess.value = true
    email.value = ''
    
    // 3秒后重置订阅成功状态
    setTimeout(() => {
      subscribeSuccess.value = false
    }, 3000)
  }, 1000)
}

// 组件挂载时加载数据
onMounted(async () => {
  await newsStore.fetchCategories()
  await newsStore.fetchNewsList()
})
</script>

<template>
  <div>
    <!-- 新闻动态头部 -->
    <section class="bg-gradient-primary text-white py-16">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">新闻动态</h1>
          <p class="text-xl opacity-90 leading-relaxed">
            了解剧投投最新动态，把握行业发展趋势，获取投资人关心的最新资讯
          </p>
        </div>
      </div>
    </section>
    
    <!-- 新闻内容区 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <!-- 分类筛选 -->
        <div class="mb-10 flex flex-wrap justify-center">
          <button 
            v-for="category in categories" 
            :key="category"
            @click="selectCategory(category)"
            class="px-4 py-2 m-1 rounded-full transition-colors"
            :class="selectedCategory === category ? 'bg-gradient-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
          >
            {{ category }}
          </button>
        </div>
        
        <!-- 新闻列表 - 时间轴样式 -->
        <div class="max-w-4xl mx-auto relative">
          <!-- 中心竖线 -->
          <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gray-200"></div>
          
          <!-- 新闻项目 -->
          <div v-for="(news, index) in filteredNews" :key="news.id" class="mb-12 relative">
            <!-- 时间点 -->
            <div class="absolute left-1/2 transform -translate-x-1/2 -mt-2 w-4 h-4 rounded-full bg-primary border-4 border-white shadow-md z-10"></div>
            
            <!-- 日期 (小屏幕隐藏) -->
            <div class="absolute top-0 left-1/2 transform -translate-x-1/2 md:left-auto md:transform-none md:right-[53%] text-center md:text-right py-1 px-3 bg-primary/10 text-primary text-sm font-medium rounded-full hidden md:block">
              {{ news.date }}
            </div>
            
            <!-- 新闻卡片 -->
            <div 
              class="relative md:w-[45%] ml-auto md:ml-[55%] mt-8 md:mt-0"
              :class="{'md:ml-0 md:mr-[55%]': index % 2 !== 0}"
            >
              <!-- 移动端日期显示 -->
              <div class="md:hidden mb-2 text-sm text-gray-500">
                {{ news.date }} · {{ news.category }}
              </div>
              
              <div class="card overflow-hidden hover:shadow-lg transition-all">
                <!-- 使用彩色背景替代图片 -->
                <div :class="['w-full h-48 flex items-center justify-center', news.bgColor]">
                  <span class="text-white text-xl font-bold">{{ news.category }}</span>
                </div>
                
                <div class="p-6">
                  <div class="flex justify-between items-center mb-3">
                    <span class="text-sm text-primary font-medium">{{ news.category }}</span>
                    <span class="text-sm text-gray-500 hidden md:block">{{ news.date }}</span>
                  </div>
                  
                  <h3 class="text-xl font-bold mb-3">{{ news.title }}</h3>
                  <p class="text-gray-600 mb-4">{{ news.summary }}</p>
                  
                  <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-500">{{ news.author }}</span>
                    <router-link :to="`/news/${news.id}`" class="text-primary hover:text-primary-dark font-medium flex items-center">
                      阅读详情
                      <svg class="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </router-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="flex justify-center mt-12">
          <nav class="flex items-center">
            <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full border border-gray-300 mr-2 text-gray-600 hover:bg-gray-100">
              <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </a>
            
            <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full bg-primary text-white mr-2">1</a>
            <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full border border-gray-300 mr-2 text-gray-600 hover:bg-gray-100">2</a>
            <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full border border-gray-300 mr-2 text-gray-600 hover:bg-gray-100">3</a>
            
            <span class="mx-2 text-gray-500">...</span>
            
            <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full border border-gray-300 mr-2 text-gray-600 hover:bg-gray-100">8</a>
            
            <a href="#" class="w-10 h-10 flex items-center justify-center rounded-full border border-gray-300 text-gray-600 hover:bg-gray-100">
              <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </nav>
        </div>
      </div>
    </section>
    
    <!-- 订阅区 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto text-center">
          <h2 class="text-3xl font-bold mb-6 text-gradient">订阅行业资讯</h2>
          <p class="text-gray-600 mb-8">
            订阅剧投投的行业资讯，第一时间获取最新的短剧市场动态、投资机会和项目进展
          </p>
          
          <form @submit.prevent="handleSubscribe" class="flex flex-col sm:flex-row gap-3">
            <input 
              v-model="email" 
              type="email" 
              placeholder="您的邮箱地址" 
              class="flex-grow px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary"
              required
            />
            <button 
              type="submit" 
              class="btn btn-primary px-6 py-3 whitespace-nowrap"
              :disabled="isSubmitting || subscribeSuccess"
            >
              <span v-if="isSubmitting">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                处理中...
              </span>
              <span v-else-if="subscribeSuccess">
                <svg class="-ml-1 mr-2 h-4 w-4 text-white inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                订阅成功!
              </span>
              <span v-else>订阅</span>
            </button>
          </form>
          
          <p class="text-sm text-gray-500 mt-3">
            我们尊重您的隐私，不会向第三方透露您的邮箱信息
          </p>
        </div>
      </div>
    </section>
  </div>
</template> 