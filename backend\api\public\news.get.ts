import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';

/**
 * 获取公开新闻列表接口
 * GET /api/public/news
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query_params = getQuery(event);
    const { 
      page = 1, 
      pageSize = 12, 
      category, 
      search, 
      featured,
      orderBy = 'publish_date',
      orderDirection = 'DESC'
    } = query_params;

    // 参数验证
    const pageNum = Math.max(1, parseInt(page as string) || 1);
    const pageSizeNum = Math.min(50, Math.max(1, parseInt(pageSize as string) || 12));
    const offset = (pageNum - 1) * pageSizeNum;

    // 构建查询条件
    let whereConditions = ['n.status = ?'];
    let queryParams = ['published'];

    // 分类筛选
    if (category && category !== 'all') {
      if (typeof category === 'string' && category.trim()) {
        whereConditions.push('(nc.slug = ? OR nc.name = ?)');
        queryParams.push(category.trim(), category.trim());
      }
    }

    // 搜索条件
    if (search && typeof search === 'string' && search.trim()) {
      whereConditions.push('(n.title LIKE ? OR n.summary LIKE ? OR n.content LIKE ?)');
      const searchTerm = `%${search.trim()}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }

    // 推荐筛选
    if (featured === 'true') {
      whereConditions.push('n.is_featured = ?');
      queryParams.push(1);
    }

    const whereClause = whereConditions.join(' AND ');

    // 构建排序条件
    const allowedOrderBy = ['publish_date', 'view_count', 'created_at', 'title'];
    const orderByField = allowedOrderBy.includes(orderBy as string) ? orderBy : 'publish_date';
    const orderDir = orderDirection === 'ASC' ? 'ASC' : 'DESC';

    // 查询新闻列表
    const newsQuery = `
      SELECT 
        n.id,
        n.title,
        n.summary,
        n.cover_image_url,
        n.author,
        n.view_count,
        n.is_featured,
        n.publish_date,
        n.created_at,
        nc.id as category_id,
        nc.name as category_name,
        nc.slug as category_slug
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      WHERE ${whereClause}
        AND n.publish_date <= NOW()
      ORDER BY n.${orderByField} ${orderDir}
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      WHERE ${whereClause}
        AND n.publish_date <= NOW()
    `;

    // 执行查询
    const [newsList, countResult] = await Promise.all([
      query(newsQuery, queryParams),
      query(countQuery, queryParams)
    ]);

    const total = countResult[0]?.total || 0;
    const totalPages = Math.ceil(total / pageSizeNum);

    // 格式化新闻数据
    const formattedNews = newsList.map((news: any) => ({
      id: news.id,
      title: news.title,
      summary: news.summary,
      coverImage: news.cover_image_url,
      author: news.author,
      viewCount: news.view_count,
      isFeatured: news.is_featured === 1,
      publishDate: news.publish_date,
      createdAt: news.created_at,
      category: news.category_id ? {
        id: news.category_id,
        name: news.category_name,
        slug: news.category_slug
      } : null
    }));

    return {
      success: true,
      data: {
        list: formattedNews,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };

  } catch (error: any) {
    logger.error('获取公开新闻列表失败', {
      error: error.message,
      stack: error.stack,
      ip: getClientIP(event)
    });

    return {
      success: false,
      message: '获取新闻列表失败',
      data: {
        list: [],
        pagination: {
          page: 1,
          pageSize: 12,
          total: 0,
          totalPages: 0
        }
      }
    };
  }
});
