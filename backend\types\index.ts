// 用户类型枚举
export type UserType = 'investor' | 'producer' | 'fund_manager';

// 用户相关类型
export interface User {
  id: number;
  username: string;
  email?: string;
  phone?: string;
  password?: string;
  password_hash?: string; // 兼容字段
  real_name?: string;
  avatar?: string;
  user_type: UserType;
  company_name?: string;
  id_card?: string;
  business_license?: string;
  status: number;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
  reset_token?: string;
  reset_token_expiry?: string;
  tokens_invalidated_at?: string;
}

export interface CreateUserData {
  username: string;
  email?: string;
  phone?: string;
  password: string;
  real_name?: string;
  avatar?: string;
  user_type: UserType;
  company_name?: string;
  id_card?: string;
  business_license?: string;
  status?: number;
}

export interface UpdateUserData {
  username?: string;
  email?: string;
  phone?: string;
  real_name?: string;
  avatar?: string;
  user_type?: UserType;
  company_name?: string;
  id_card?: string;
  business_license?: string;
  status?: number;
}

// 基金相关类型
export interface Fund {
  id: number;
  code: string;
  title: string;
  description?: string;
  type: 'equity' | 'debt' | 'mixed';
  risk: 'R1' | 'R2' | 'R3' | 'R4' | 'R5';
  min_investment: number;
  period: 'short' | 'medium' | 'long';
  target_size: number;
  raised_amount: number;
  expected_return?: string;
  min_holding_period: number;
  risk_description?: string;
  establish_date?: string;
  exit_date?: string;
  manager?: string;
  trustee?: string;
  redemption_policy?: string;
  investment_strategy?: string;
  is_published: number; // 0 or 1
  created_at: string;
  updated_at: string;
}

// 基金亮点
export interface FundHighlight {
  id: number;
  fund_id: number;
  content: string;
  sort_order: number;
  created_at: string;
}

// 基金文档
export interface FundDocument {
  id: number;
  fund_id: number;
  name: string;
  file_url: string;
  file_type?: string;
  file_size?: string;
  created_at: string;
}

// 短剧文档
export interface DramaDocument {
  id: number;
  drama_id: number;
  name: string;
  file_url: string;
  file_type?: string;
  file_size?: string;
  created_at: string;
  updated_at?: string;
}

// 基金FAQ
export interface FundFaq {
  id: number;
  fund_id: number;
  question: string;
  answer: string;
  sort_order: number;
  created_at: string;
}

// 基金时间线
export interface FundTimeline {
  id: number;
  fund_id: number;
  stage: string;
  date: string;
  status: 'active' | 'pending' | 'completed';
  sort_order: number;
  created_at: string;
}

// 基金费用
export interface FundFee {
  id: number;
  fund_id: number;
  name: string;
  value: string;
  sort_order: number;
  created_at: string;
}

// 基金历史业绩
export interface FundPerformance {
  id: number;
  fund_id: number;
  year: string;
  return: string;
  average?: string;
  created_at: string;
}

// 基金资金使用计划
export interface FundUsagePlan {
  id: number;
  fund_id: number;
  percentage: number;
  description: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 基金成功案例
export interface FundSuccessCase {
  id: number;
  fund_id: number;
  title: string;
  description?: string;
  return_rate?: string;
  investment_amount?: string;
  recovery_period?: string;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 创建基金数据
export interface CreateFundData {
  code: string;
  title: string;
  description?: string;
  type: 'equity' | 'debt' | 'mixed';
  risk: 'R1' | 'R2' | 'R3' | 'R4' | 'R5';
  min_investment: number;
  period: 'short' | 'medium' | 'long';
  target_size: number;
  raised_amount?: number;
  expected_return?: string;
  min_holding_period: number;
  risk_description?: string;
  establish_date?: string;
  exit_date?: string;
  manager?: string;
  trustee?: string;
  redemption_policy?: string;
  investment_strategy?: string;
  is_published?: number;
}

// 更新基金数据
export interface UpdateFundData extends Partial<CreateFundData> {
  id: number;
}

// 基金详情（包含关联数据）
export interface FundDetail extends Fund {
  highlights: FundHighlight[];
  documents: FundDocument[];
  faqs: FundFaq[];
  timelines: FundTimeline[];
  fees: FundFee[];
  performances: FundPerformance[];
  usagePlans: FundUsagePlan[];
  successCases: FundSuccessCase[];
}

// 轮播图相关类型
export interface Banner {
  id: number;
  title: string;
  image_url: string;
  link_url?: string;
  description?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateBannerData {
  title: string;
  image_url: string;
  link_url?: string;
  description?: string;
  sort_order?: number;
  is_active?: boolean;
}

// 艺人相关类型
export interface Actor {
  id: number;
  name: string;
  avatar_url?: string;
  bio?: string;
  tags?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateActorData {
  name: string;
  avatar_url?: string;
  bio?: string;
  tags?: string;
  sort_order?: number;
  is_active?: boolean;
}

// 系统设置相关类型
export interface SystemSetting {
  id: number;
  key: string;
  value: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

// 审计日志相关类型
export interface AuditLog {
  id: number;
  user_id?: number;
  username?: string;
  user_type?: 'admin' | 'user' | 'investor';
  action: string;
  description: string;
  ip?: string;
  user_agent?: string;
  path?: string;
  method?: string;
  status_code?: number;
  response_time?: number;
  metadata?: string;
  created_at: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  code: number;
  data: T;
  message: string;
  error?: any;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 请求参数类型
export interface PaginationQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email?: string;
  phone?: string;
  password: string;
  real_name?: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

// 文件上传相关类型
export interface UploadedFile {
  filename: string;
  originalname: string;
  mimetype: string;
  size: number;
  url: string;
  path: string;
}

export interface UploadResponse {
  success: boolean;
  file?: UploadedFile;
  message: string;
  error?: string;
}

// JWT相关类型
export interface JWTPayload {
  userId: number;
  username: string;
  is_admin: boolean;
  email?: string;
  real_name?: string;
  iat: number;
  exp: number;
}

// 环境配置类型
export interface RuntimeConfig {
  dbHost: string;
  dbPort: string;
  dbUser: string;
  dbPassword: string;
  dbName: string;
  jwtSecret: string;
  jwtExpiresIn: string;
  jwtAdminExpiresIn: string;
  uploadDir: string;
  maxFileSize: string;
  allowedFileTypes: string;
  appUrl: string;
  apiUrl: string;
  smtpHost: string;
  smtpPort: string;
  smtpUser: string;
  smtpPass: string;
  smtpFrom: string;
  ossProvider: string;
  aliOssRegion: string;
  aliOssAccessKeyId: string;
  aliOssAccessKeySecret: string;
  aliOssBucket: string;
}
