import { request } from './request'
import type { ApiResponse } from '@/types'

// 新闻相关类型定义
export interface NewsItem {
  id: number
  title: string
  summary?: string
  content: string
  coverImage?: string
  author?: string
  sourceUrl?: string
  status: 'draft' | 'pending' | 'published' | 'archived'
  isFeatured: boolean
  viewCount: number
  publishDate?: string
  createdAt: string
  updatedAt: string
  category?: NewsCategory
  tags?: NewsTag[]
  relatedNews?: NewsItem[]
  seo?: NewsSeo
}

export interface NewsCategory {
  id: number
  name: string
  slug: string
  description?: string
  parentId?: number
  sortOrder: number
  newsCount?: number
}

export interface NewsTag {
  id: number
  name: string
  usageCount?: number
}

export interface NewsSeo {
  metaTitle?: string
  metaDescription?: string
  metaKeywords?: string
  canonicalUrl?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
}

export interface NewsListParams {
  page?: number
  pageSize?: number
  category?: string
  search?: string
  featured?: boolean
  orderBy?: string
  orderDirection?: 'ASC' | 'DESC'
}

export interface NewsListResponse {
  list: NewsItem[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

export interface CategoriesResponse {
  categories: NewsCategory[]
  flatCategories: NewsCategory[]
  popularTags: NewsTag[]
}

/**
 * 获取新闻列表
 */
export function getNewsList(params: NewsListParams = {}) {
  return request<ApiResponse<NewsListResponse>>({
    url: '/api/public/news',
    method: 'GET',
    params
  })
}

/**
 * 获取新闻详情
 */
export function getNewsDetail(id: number) {
  return request<ApiResponse<NewsItem>>({
    url: `/api/public/news/${id}`,
    method: 'GET'
  })
}

/**
 * 获取新闻分类列表
 */
export function getNewsCategories() {
  return request<ApiResponse<CategoriesResponse>>({
    url: '/api/public/news/categories',
    method: 'GET'
  })
}

/**
 * 增加新闻阅读量
 */
export function increaseNewsView(id: number) {
  return request<ApiResponse<{ newsId: number; viewCount: number; incremented: boolean }>>({
    url: `/api/public/news/${id}/view`,
    method: 'POST'
  })
}

/**
 * 获取推荐新闻列表
 */
export function getFeaturedNews(limit: number = 6) {
  return getNewsList({
    featured: true,
    pageSize: limit,
    orderBy: 'publish_date',
    orderDirection: 'DESC'
  })
}

/**
 * 获取最新新闻列表
 */
export function getLatestNews(limit: number = 10) {
  return getNewsList({
    pageSize: limit,
    orderBy: 'publish_date',
    orderDirection: 'DESC'
  })
}

/**
 * 根据分类获取新闻列表
 */
export function getNewsByCategory(category: string, params: Omit<NewsListParams, 'category'> = {}) {
  return getNewsList({
    ...params,
    category
  })
}

/**
 * 搜索新闻
 */
export function searchNews(keyword: string, params: Omit<NewsListParams, 'search'> = {}) {
  return getNewsList({
    ...params,
    search: keyword
  })
}

/**
 * 获取热门新闻（按阅读量排序）
 */
export function getPopularNews(limit: number = 10) {
  return getNewsList({
    pageSize: limit,
    orderBy: 'view_count',
    orderDirection: 'DESC'
  })
}
