export default defineEventHandler((event) => {
  // 只处理非API路由
  if (event.node.req.url?.startsWith('/api/')) {
    // 让API路由继续处理
    return;
  }

  return `
<h1>剧投投后端服务</h1>
<h2>API服务正在运行</h2>
<ul>
<li><a href="/api/health">/api/health</a> - 健康检查</li>
<li><a href="/api/public/config">/api/public/config</a> - 公共配置</li>
<li><a href="/api/public/banners">/api/public/banners</a> - 公开横幅</li>
<li><a href="/api/auth/login">/api/auth/login</a> - 用户登录</li>
<li><a href="/api/auth/admin/login">/api/auth/admin/login</a> - 管理员登录</li>
</ul>
<p>更多API请参考文档</p>
`;
});
