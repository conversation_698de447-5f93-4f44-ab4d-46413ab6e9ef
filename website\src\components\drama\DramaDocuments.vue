<template>
  <div class="bg-white rounded-xl shadow-md p-6 mb-8">
    <h3 class="text-xl font-bold mb-4">剧本和计划书</h3>
    
    <!-- 文档列表 -->
    <div v-if="documents && documents.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div 
        v-for="doc in documents" 
        :key="doc.id"
        class="border rounded-lg p-4 flex items-center justify-between hover:border-primary transition-colors cursor-pointer"
        @click="downloadDocument(doc)"
      >
        <div class="flex items-center">
          <div class="bg-gray-100 rounded-lg p-2 mr-4">
            <!-- 根据文件类型显示不同图标 -->
            <svg v-if="isPdfFile(doc.fileType)" class="w-10 h-10 text-red-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <svg v-else-if="isWordFile(doc.fileType)" class="w-10 h-10 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <svg v-else-if="isPptFile(doc.fileType)" class="w-10 h-10 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            <svg v-else class="w-10 h-10 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <div class="font-medium text-gray-900">{{ doc.name }}</div>
            <div class="text-sm text-gray-500">
              {{ doc.fileType?.toUpperCase() || 'FILE' }} · {{ doc.fileSize || '未知大小' }}
            </div>
          </div>
        </div>
        <button 
          @click.stop="downloadDocument(doc)"
          class="text-primary hover:text-primary-dark transition-colors focus:outline-none p-2 rounded-full hover:bg-gray-100"
          :title="`下载 ${doc.name}`"
        >
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else class="text-center py-8">
      <div class="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
        <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <p class="text-gray-500 text-sm">暂无剧本和计划书文档</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

// 定义props
const props = defineProps({
  documents: {
    type: Array,
    default: () => []
  }
})

// 文件类型判断函数
const isPdfFile = (fileType) => {
  return fileType && fileType.toLowerCase() === 'pdf'
}

const isWordFile = (fileType) => {
  return fileType && ['doc', 'docx'].includes(fileType.toLowerCase())
}

const isPptFile = (fileType) => {
  return fileType && ['ppt', 'pptx'].includes(fileType.toLowerCase())
}

// 下载文档函数
const downloadDocument = (doc) => {
  try {
    // 使用Blob和URL.createObjectURL强制文件下载而不是打开
    fetch(doc.fileUrl)
      .then(response => response.blob())
      .then(blob => {
        // 创建Blob URL
        const blobUrl = URL.createObjectURL(blob);
        
        // 创建一个隐藏的a标签用于下载
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = blobUrl;
        
        // 使用文档原始名称作为下载文件名
        link.download = doc.name;
        
        // 将链接添加到文档中并模拟点击
        document.body.appendChild(link);
        link.click();
        
        // 延迟清理DOM和释放URL对象
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(blobUrl);
        }, 100);
      })
      .catch(error => {
        console.error('下载文档失败:', error);
        // 如果fetch失败，尝试直接打开链接
        window.open(doc.fileUrl, '_blank');
      });
  } catch (error) {
    console.error('下载文档时发生错误:', error);
    // 降级处理：直接打开链接
    window.open(doc.fileUrl, '_blank');
  }
}
</script>

<style scoped>
/* 自定义样式 */
.border-primary {
  border-color: #3b82f6;
}

.text-primary {
  color: #3b82f6;
}

.text-primary-dark {
  color: #2563eb;
}

.hover\:border-primary:hover {
  border-color: #3b82f6;
}

.hover\:text-primary-dark:hover {
  color: #2563eb;
}
</style>
