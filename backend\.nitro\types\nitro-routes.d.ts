// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/actors': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/actors/index.get').default>>>>
    }
    '/api/admin/actors': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/actors/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/actors/index.post').default>>>>
    }
    '/api/admin/actors/selector': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/actors/selector.get').default>>>>
    }
    '/api/admin/audit-logs': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/audit-logs/index.get').default>>>>
    }
    '/api/admin/audit-logs/stats': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/audit-logs/stats.get').default>>>>
    }
    '/api/admin/banners/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/banners/[id].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/banners/[id].put').default>>>>
    }
    '/api/admin/banners/batch-delete': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/banners/batch-delete.post').default>>>>
    }
    '/api/admin/banners': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/banners/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/banners/index.post').default>>>>
    }
    '/api/admin/banners/sort': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/banners/sort.post').default>>>>
    }
    '/api/admin/brand-management/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/brand-management/[id].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/brand-management/[id].put').default>>>>
    }
    '/api/admin/brand-management/:id/status': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/brand-management/[id]/status.put').default>>>>
    }
    '/api/admin/brand-management': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/brand-management/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/brand-management/index.post').default>>>>
    }
    '/api/admin/brand-management/selector': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/brand-management/selector.get').default>>>>
    }
    '/api/admin/content-management/platforms/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/platforms/[id].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/platforms/[id].put').default>>>>
    }
    '/api/admin/content-management/platforms': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/platforms/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/platforms/index.post').default>>>>
    }
    '/api/admin/content-management/platforms/selector': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/platforms/selector.get').default>>>>
    }
    '/api/admin/content-management/tags/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/tags/[id].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/tags/[id].put').default>>>>
    }
    '/api/admin/content-management/tags': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/tags/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/tags/index.post').default>>>>
    }
    '/api/admin/content-management/tags/selector': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/content-management/tags/selector.get').default>>>>
    }
    '/api/admin/dashboard': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dashboard.get').default>>>>
    }
    '/api/admin/dramas/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id].get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id].put').default>>>>
    }
    '/api/admin/dramas/:id/additional-info': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/additional-info.put').default>>>>
    }
    '/api/admin/dramas/:id/documents': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/documents.post').default>>>>
    }
    '/api/admin/dramas/:id/documents/:documentId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/documents/[documentId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/documents/[documentId].put').default>>>>
    }
    '/api/admin/dramas/:id/documents/upload': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/documents/upload.post').default>>>>
    }
    '/api/admin/dramas/:id/funding': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/funding.put').default>>>>
    }
    '/api/admin/dramas/:id/investment-tiers': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/investment-tiers.put').default>>>>
    }
    '/api/admin/dramas/:id/materials': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/materials.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/materials.post').default>>>>
    }
    '/api/admin/dramas/:id/materials/:materialId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/materials/[materialId].delete').default>>>>
    }
    '/api/admin/dramas/:id/schedule': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/[id]/schedule.put').default>>>>
    }
    '/api/admin/dramas': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/dramas/index.post').default>>>>
    }
    '/api/admin/funds/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id].get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id].put').default>>>>
    }
    '/api/admin/funds/:id/documents': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/documents.post').default>>>>
    }
    '/api/admin/funds/:id/documents/:documentId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/documents/[documentId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/documents/[documentId].put').default>>>>
    }
    '/api/admin/funds/:id/documents/upload': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/documents/upload.post').default>>>>
    }
    '/api/admin/funds/:id/faqs': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/faqs.post').default>>>>
    }
    '/api/admin/funds/:id/faqs/:faqId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/faqs/[faqId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/faqs/[faqId].put').default>>>>
    }
    '/api/admin/funds/:id/fees': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/fees.post').default>>>>
    }
    '/api/admin/funds/:id/fees/:feeId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/fees/[feeId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/fees/[feeId].put').default>>>>
    }
    '/api/admin/funds/:id/highlights': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/highlights.post').default>>>>
    }
    '/api/admin/funds/:id/highlights/:highlightId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/highlights/[highlightId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/highlights/[highlightId].put').default>>>>
    }
    '/api/admin/funds/:id/performances': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/performances.post').default>>>>
    }
    '/api/admin/funds/:id/performances/:performanceId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/performances/[performanceId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/performances/[performanceId].put').default>>>>
    }
    '/api/admin/funds/:id/success-cases': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/success-cases.post').default>>>>
    }
    '/api/admin/funds/:id/success-cases/:caseId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/success-cases/[caseId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/success-cases/[caseId].put').default>>>>
    }
    '/api/admin/funds/:id/timelines': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/timelines.post').default>>>>
    }
    '/api/admin/funds/:id/timelines/:timelineId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/timelines/[timelineId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/timelines/[timelineId].put').default>>>>
    }
    '/api/admin/funds/:id/usage-plans': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/usage-plans.post').default>>>>
    }
    '/api/admin/funds/:id/usage-plans/:planId': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/usage-plans/[planId].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/[id]/usage-plans/[planId].put').default>>>>
    }
    '/api/admin/funds': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/index.post').default>>>>
    }
    '/api/admin/funds/stats': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/funds/stats.get').default>>>>
    }
    '/api/admin/menus': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/menus/index.get').default>>>>
    }
    '/api/admin/permissions': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/permissions.get').default>>>>
    }
    '/api/admin/roles': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles.post').default>>>>
    }
    '/api/admin/roles/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles/[id].get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles/[id].put').default>>>>
    }
    '/api/admin/roles/:id/permissions': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles/[id]/permissions.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles/[id]/permissions.post').default>>>>
    }
    '/api/admin/roles/:id/status': {
      'patch': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/roles/[id]/status.patch').default>>>>
    }
    '/api/admin/settings/cos': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/cos.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/cos.post').default>>>>
    }
    '/api/admin/settings/email': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/email.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/email.post').default>>>>
    }
    '/api/admin/settings/email/test': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/email/test.post').default>>>>
    }
    '/api/admin/settings/investment': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/investment.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/investment.post').default>>>>
    }
    '/api/admin/settings/security': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/security.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/security.post').default>>>>
    }
    '/api/admin/settings/site': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/site.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/site.post').default>>>>
    }
    '/api/admin/settings/sms': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/sms.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/sms.post').default>>>>
    }
    '/api/admin/settings/sms/test': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/sms/test.post').default>>>>
    }
    '/api/admin/settings/system-info': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/settings/system-info.get').default>>>>
    }
    '/api/admin/system/menus': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus.post').default>>>>
    }
    '/api/admin/system/menus/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/[id].get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/[id].put').default>>>>
    }
    '/api/admin/system/menus/:id/status': {
      'patch': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/[id]/status.patch').default>>>>
    }
    '/api/admin/system/menus/name-exists': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/name-exists.get').default>>>>
    }
    '/api/admin/system/menus/path-exists': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/path-exists.get').default>>>>
    }
    '/api/admin/system/menus/sort': {
      'patch': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/system/menus/sort.patch').default>>>>
    }
    '/api/admin/talent-management/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/talent-management/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/talent-management/[id].get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/talent-management/[id].put').default>>>>
    }
    '/api/admin/talent-management': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/talent-management/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/talent-management/index.post').default>>>>
    }
    '/api/admin/upload/actor-avatar': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/actor-avatar.post').default>>>>
    }
    '/api/admin/upload/banner-image': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/banner-image.post').default>>>>
    }
    '/api/admin/upload/brand-image': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/brand-image.post').default>>>>
    }
    '/api/admin/upload/common': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/common.post').default>>>>
    }
    '/api/admin/upload/drama-cover': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/drama-cover.post').default>>>>
    }
    '/api/admin/upload/drama-material': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/drama-material.post').default>>>>
    }
    '/api/admin/upload/file': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/file.post').default>>>>
    }
    '/api/admin/upload/platform-image': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/platform-image.post').default>>>>
    }
    '/api/admin/upload/site-asset': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/site-asset.post').default>>>>
    }
    '/api/admin/upload/site-logo': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/site-logo.post').default>>>>
    }
    '/api/admin/upload/site/favicon': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/site/favicon.post').default>>>>
    }
    '/api/admin/upload/site/logo': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/upload/site/logo.post').default>>>>
    }
    '/api/admin/user-management/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/[id].delete').default>>>>
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/[id].get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/[id].put').default>>>>
    }
    '/api/admin/user-management/:id/reset-password': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/[id]/reset-password.put').default>>>>
    }
    '/api/admin/user-management/:id/status': {
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/[id]/status.put').default>>>>
    }
    '/api/admin/user-management/batch-delete': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/batch-delete.post').default>>>>
    }
    '/api/admin/user-management/batch-status': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/batch-status.post').default>>>>
    }
    '/api/admin/user-management/export': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/export.get').default>>>>
    }
    '/api/admin/user-management': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/index.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/index.post').default>>>>
    }
    '/api/admin/user-management/stats': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/admin/user-management/stats.get').default>>>>
    }
    '/api/auth/admin/info': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/admin/info.get').default>>>>
    }
    '/api/auth/admin/login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/admin/login.post').default>>>>
    }
    '/api/auth/admin/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/admin/logout.post').default>>>>
    }
    '/api/auth/codes': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/codes.get').default>>>>
    }
    '/api/auth/forgot-password': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/forgot-password.post').default>>>>
    }
    '/api/auth/login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/login.post').default>>>>
    }
    '/api/auth/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/logout.post').default>>>>
    }
    '/api/auth/refresh': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/refresh.post').default>>>>
    }
    '/api/auth/register': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/register.post').default>>>>
    }
    '/api/brands': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/brands/index.get').default>>>>
    }
    '/api/debug/project': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/debug/project.get').default>>>>
    }
    '/api/dramas/:id': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/dramas/[id].get').default>>>>
    }
    '/api/dramas': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/dramas/index.get').default>>>>
    }
    '/api/funds/:code': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/funds/[code].get').default>>>>
    }
    '/api/funds': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/funds/index.get').default>>>>
    }
    '/api/health': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/health.get').default>>>>
    }
    '/api/platforms': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/platforms/index.get').default>>>>
    }
    '/api/public/banners': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/public/banners.get').default>>>>
    }
    '/api/public/config': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/public/config.get').default>>>>
    }
    '/api/public/settings': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/public/settings/index.get').default>>>>
    }
    '/api/public/settings/site': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/public/settings/site.get').default>>>>
    }
    '/api/public/system-info': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/public/system-info.get').default>>>>
    }
    '/api/tags': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/tags/index.get').default>>>>
    }
    '/api/users/change-password': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/change-password.post').default>>>>
    }
    '/api/users/dashboard': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/dashboard.get').default>>>>
    }
    '/api/users/investments/create': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/investments/create.post').default>>>>
    }
    '/api/users/investments/records': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/investments/records.get').default>>>>
    }
    '/api/users/profile': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/profile.get').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/profile.put').default>>>>
    }
    '/api/users/wallet/balance': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/balance.get').default>>>>
    }
    '/api/users/wallet/bank-cards': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/bank-cards.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/bank-cards.post').default>>>>
    }
    '/api/users/wallet/payment-methods': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/payment-methods.get').default>>>>
    }
    '/api/users/wallet/recharge': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/recharge.post').default>>>>
    }
    '/api/users/wallet/recharge/:orderId': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/recharge/[orderId].get').default>>>>
    }
    '/api/users/wallet/withdraw': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/users/wallet/withdraw.post').default>>>>
    }
    '/api/website/recent-investments': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/website/recent-investments.get').default>>>>
    }
    '/api/website/stats': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/website/stats.get').default>>>>
    }
    '/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/[...]').default>>>>
    }
  }
}
export {}