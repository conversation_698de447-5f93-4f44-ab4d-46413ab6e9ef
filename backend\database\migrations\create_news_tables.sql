-- 创建新闻相关数据库表
-- 执行时间：2024-08-01

-- 1. 创建新闻分类表
CREATE TABLE IF NOT EXISTS news_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  slug VARCHAR(100) UNIQUE COMMENT 'URL别名',
  description TEXT COMMENT '分类描述',
  parent_id INT DEFAULT NULL COMMENT '父分类ID',
  sort_order INT DEFAULT 0 COMMENT '排序',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_parent (parent_id),
  INDEX idx_sort (sort_order),
  INDEX idx_slug (slug),
  FOREIGN KEY (parent_id) REFERENCES news_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻分类表';

-- 2. 创建新闻主表
CREATE TABLE IF NOT EXISTS news (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL COMMENT '标题',
  summary TEXT COMMENT '摘要',
  content LONGTEXT COMMENT '正文内容',
  cover_image_url VARCHAR(500) COMMENT '封面图片',
  category_id INT COMMENT '分类ID',
  author VARCHAR(100) COMMENT '作者',
  source_url VARCHAR(500) COMMENT '来源链接',
  status ENUM('draft', 'pending', 'published', 'archived') DEFAULT 'draft' COMMENT '状态：草稿、待审核、已发布、已归档',
  is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
  view_count INT DEFAULT 0 COMMENT '阅读量',
  publish_date DATETIME COMMENT '发布时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_category (category_id),
  INDEX idx_status (status),
  INDEX idx_publish_date (publish_date),
  INDEX idx_featured (is_featured),
  INDEX idx_view_count (view_count),
  FOREIGN KEY (category_id) REFERENCES news_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻主表';

-- 3. 创建新闻标签表
CREATE TABLE IF NOT EXISTS news_tags (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL UNIQUE COMMENT '标签名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻标签表';

-- 4. 创建新闻标签关联表
CREATE TABLE IF NOT EXISTS news_tag_relations (
  news_id INT NOT NULL COMMENT '新闻ID',
  tag_id INT NOT NULL COMMENT '标签ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (news_id, tag_id),
  FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE,
  FOREIGN KEY (tag_id) REFERENCES news_tags(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻标签关联表';

-- 5. 插入初始分类数据
INSERT INTO news_categories (name, slug, description, sort_order) VALUES
('行业动态', 'industry-news', '短剧行业最新动态和趋势分析', 1),
('投资资讯', 'investment-news', '投资相关的新闻和分析报告', 2),
('项目更新', 'project-updates', '平台项目的最新进展和更新', 3),
('政策法规', 'policy-regulations', '相关政策法规和行业规范', 4),
('公司新闻', 'company-news', '剧投投公司相关新闻和公告', 5),
('市场分析', 'market-analysis', '市场趋势和数据分析报告', 6);

-- 6. 插入常用标签数据
INSERT INTO news_tags (name) VALUES
('短剧'),
('投资'),
('融资'),
('市场'),
('政策'),
('技术'),
('合作'),
('发布'),
('分析'),
('趋势'),
('平台'),
('用户'),
('内容'),
('收益'),
('风险');

-- 7. 创建新闻阅读记录表（可选，用于统计用户阅读行为）
CREATE TABLE IF NOT EXISTS news_read_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  news_id INT NOT NULL COMMENT '新闻ID',
  user_id INT DEFAULT NULL COMMENT '用户ID（可为空，支持匿名阅读）',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
  INDEX idx_news (news_id),
  INDEX idx_user (user_id),
  INDEX idx_read_at (read_at),
  FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻阅读记录表';

-- 8. 创建新闻SEO信息表（可选，用于SEO优化）
CREATE TABLE IF NOT EXISTS news_seo (
  id INT PRIMARY KEY AUTO_INCREMENT,
  news_id INT NOT NULL UNIQUE COMMENT '新闻ID',
  meta_title VARCHAR(255) COMMENT 'SEO标题',
  meta_description TEXT COMMENT 'SEO描述',
  meta_keywords VARCHAR(500) COMMENT 'SEO关键词',
  canonical_url VARCHAR(500) COMMENT '规范URL',
  og_title VARCHAR(255) COMMENT 'Open Graph标题',
  og_description TEXT COMMENT 'Open Graph描述',
  og_image VARCHAR(500) COMMENT 'Open Graph图片',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻SEO信息表';

-- 执行完成提示
SELECT 'News tables created successfully!' as message;
