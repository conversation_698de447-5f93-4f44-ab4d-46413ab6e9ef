/**
 * 获取公开网站设置接口
 * GET /api/public/settings/site
 */
export default defineEventHandler(async (event) => {
  try {
    // 从数据库获取网站设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['site_settings']
    );

    let siteSettings = {};
    
    if (result.length > 0 && result[0].setting_value) {
      try {
        siteSettings = JSON.parse(result[0].setting_value);
      } catch (error) {
        logger.error('解析网站设置JSON失败', { error: error.message });
      }
    }

    // 如果没有设置，返回默认值
    if (Object.keys(siteSettings).length === 0) {
      siteSettings = {
        title: '剧投投',
        description: '中国最专业的短剧投资平台',
        logo: '/images/logo.png',
        favicon: '/favicon.ico',
        icp: '粤ICP备XXXXXXXX号',
        copyright: '© 2023 剧投投. 保留所有权利'
      };
    }

    return {
      success: true,
      data: siteSettings
    };

  } catch (error: any) {
    logger.error('获取公开网站设置失败', {
      error: error.message,
      ip: getClientIP(event)
    });

    // 返回默认设置，确保前端正常工作
    return {
      success: true,
      data: {
        title: '剧投投',
        description: '中国最专业的短剧投资平台',
        logo: '/images/logo.png',
        favicon: '/favicon.ico',
        icp: '粤ICP备XXXXXXXX号',
        copyright: '© 2023 剧投投. 保留所有权利'
      }
    };
  }
});
