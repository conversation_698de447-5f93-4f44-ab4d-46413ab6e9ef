import { query } from '~/utils/database';

/**
 * 合并演员信息的辅助函数
 */
function mergeActorInfo(roleData: string | null, actorsMap: Map<number, any>): string | null {
  if (!roleData) return null;

  try {
    const parsed = JSON.parse(roleData);
    if (Array.isArray(parsed)) {
      const mergedData = parsed.map(item => {
        if (item && typeof item.id === 'number') {
          const actorInfo = actorsMap.get(item.id);
          if (actorInfo) {
            return {
              ...item,
              name: actorInfo.name,
              avatarUrl: actorInfo.avatarUrl
            };
          }
        }
        return item;
      });
      return JSON.stringify(mergedData);
    }
  } catch (e) {
    // 如果解析失败，返回原始数据
  }

  return roleData;
}

/**
 * 合并公司信息的辅助函数
 */
function mergeCompanyInfo(companyData: string | null, companiesMap: Map<number, string>): string | null {
  if (!companyData) return null;

  // 如果companiesMap为空，说明没有查询到公司信息，直接返回原始数据
  if (companiesMap.size === 0) {
    return companyData;
  }

  try {
    const parsed = JSON.parse(companyData);
    if (Array.isArray(parsed)) {
      const companyNames = parsed.map(id => {
        if (typeof id === 'number') {
          const companyName = companiesMap.get(id);
          return companyName || `未知公司(ID:${id})`;
        }
        return id;
      }).filter(Boolean);

      // 如果成功获取到公司名称，返回逗号分隔的字符串
      if (companyNames.length > 0) {
        return companyNames.join(',');
      }
    }
  } catch (e) {
    // JSON解析失败，可能是其他格式的数据
  }

  // 如果所有处理都失败，返回原始数据
  return companyData;
}

/**
 * 获取公开短剧详情接口
 * GET /api/dramas/[id]
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取短剧ID
    const dramaId = getRouterParam(event, 'id');
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: '短剧ID不能为空'
      });
    }

    // 验证ID格式
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的短剧ID'
      });
    }

    // 查询短剧详情（联合查询所有相关表，包含完整的创作团队信息）
    const dramaResult = await query(`
      SELECT
        ds.id, ds.title, ds.cover, ds.tags, ds.description, ds.episodes, ds.episode_length,
        ds.target_platform, ds.projected_views, ds.cast, ds.is_online,
        ds.created_at, ds.updated_at,
        dpt.production_company, dpt.co_production_company, dpt.executive_producer,
        dpt.co_executive_producer, dpt.chief_producer, dpt.producer, dpt.co_producer,
        dpt.director, dpt.scriptwriter, dpt.supervisor, dpt.coordinator,
        dps.schedule_pre_production, dps.schedule_filming, dps.schedule_post_production,
        dps.expected_release_date,
        dfi.funding_goal, dfi.current_funding, dfi.funding_end_date, dfi.expected_return,
        dfi.min_investment, dfi.funding_share, dfi.roi, dfi.status,
        CASE
          WHEN dfi.funding_end_date IS NULL THEN 0
          ELSE GREATEST(0, DATEDIFF(dfi.funding_end_date, NOW()))
        END as remaining_days
      FROM drama_series ds
      LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
      LEFT JOIN drama_production_schedule dps ON ds.id = dps.drama_id
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      WHERE ds.id = ? AND ds.is_online = 1
    `, [id]) as any[];

    if (dramaResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: '短剧不存在或未上线'
      });
    }

    const drama = dramaResult[0];

    // 查询短剧素材
    const materialsResult = await query(`
      SELECT id, drama_id, title, url, thumbnail, type, sort_order, created_at, updated_at
      FROM drama_materials
      WHERE drama_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `, [id]) as any[];

    // 查询短剧文档
    const documentsResult = await query(`
      SELECT id, drama_id, name, file_url, file_type, file_size, created_at, updated_at
      FROM drama_documents
      WHERE drama_id = ?
      ORDER BY created_at DESC
    `, [id]) as any[];

    // 查询其他信息
    const additionalInfoResult = await query(`
      SELECT risk_management, confirmed_resources, investment_tiers
      FROM drama_additional_info
      WHERE drama_id = ?
    `, [id]) as any[];

    // 查询投资权益档位
    const investmentTiersResult = await query(`
      SELECT id, tier_name, min_amount, max_amount, benefits, return_rate,
             limited_quantity, sold_quantity, sort_order, is_active
      FROM drama_investment_tiers
      WHERE drama_id = ? AND is_active = 1
      ORDER BY sort_order ASC, created_at ASC
    `, [id]) as any[];

    // 查询出品公司营业执照信息
    let businessLicenseInfo = null;
    try {
      // 从短剧详情中获取出品公司ID（production_company字段）
      if (drama && drama.production_company) {
        const productionCompanyIds = JSON.parse(drama.production_company);
        if (Array.isArray(productionCompanyIds) && productionCompanyIds.length > 0) {
          const firstCompanyId = productionCompanyIds[0];
          console.log('查询营业执照，公司ID:', firstCompanyId);

          const businessLicenseResult = await query(`
            SELECT id, company_name, business_license_photo
            FROM brands
            WHERE id = ?
          `, [firstCompanyId]) as any[];

          console.log('营业执照查询结果:', businessLicenseResult);

          if (businessLicenseResult.length > 0) {
            businessLicenseInfo = {
              companyId: businessLicenseResult[0].id,
              companyName: businessLicenseResult[0].company_name,
              businessLicensePhoto: businessLicenseResult[0].business_license_photo
            };
          }
        }
      }
    } catch (error) {
      console.error('查询营业执照信息失败:', error);
    }

    // 查询投资人信息
    let investorsResult = [];
    let totalInvestors = 0;

    try {
      investorsResult = await query(`
        SELECT
          u.id, u.username, u.real_name,
          ui.investment_amount, ui.investment_date
        FROM user_investments ui
        LEFT JOIN users u ON ui.user_id = u.id
        WHERE ui.project_id = ? AND ui.investment_status = 'active'
        ORDER BY ui.investment_date DESC
        LIMIT 20
      `, [id]) as any[];

      // 查询投资人总数
      const investorCountResult = await query(`
        SELECT COUNT(DISTINCT user_id) as total_investors
        FROM user_investments
        WHERE project_id = ? AND investment_status = 'active'
      `, [id]) as any[];

      totalInvestors = investorCountResult[0]?.total_investors || 0;
    } catch (error) {
      // 如果查询失败，使用默认值
      console.error('投资人信息查询失败:', error);
      investorsResult = [];
      totalInvestors = 0;
    }

    // 获取所有演员ID并查询演员信息
    const actorIds = new Set<number>();

    // 从各个角色字段中提取演员ID
    const roleFields = [
      'executive_producer', 'co_executive_producer', 'chief_producer',
      'producer', 'co_producer', 'director', 'scriptwriter', 'supervisor', 'coordinator'
    ];

    roleFields.forEach(field => {
      const fieldValue = drama[field];
      if (fieldValue) {
        try {
          const parsed = JSON.parse(fieldValue);
          if (Array.isArray(parsed)) {
            parsed.forEach(item => {
              if (item && typeof item.id === 'number') {
                actorIds.add(item.id);
              }
            });
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    });

    // 查询演员信息
    let actorsMap = new Map();
    if (actorIds.size > 0) {
      const actorIdsArray = Array.from(actorIds);
      const placeholders = actorIdsArray.map(() => '?').join(',');
      const actorsResult = await query(`
        SELECT id, name, avatar_url
        FROM actors
        WHERE id IN (${placeholders})
      `, actorIdsArray) as any[];

      actorsResult.forEach(actor => {
        actorsMap.set(actor.id, {
          id: actor.id,
          name: actor.name,
          avatarUrl: actor.avatar_url
        });
      });
    }

    // 获取所有公司ID并查询公司信息
    const companyIds = new Set<number>();

    // 从公司字段中提取公司ID
    const companyFields = ['production_company', 'co_production_company'];

    companyFields.forEach(field => {
      const fieldValue = drama[field];
      if (fieldValue) {
        try {
          const parsed = JSON.parse(fieldValue);
          if (Array.isArray(parsed)) {
            parsed.forEach(id => {
              if (typeof id === 'number') {
                companyIds.add(id);
              }
            });
          }
        } catch (e) {
          // 忽略解析错误
        }
      }
    });

    // 查询公司信息
    let companiesMap = new Map();
    if (companyIds.size > 0) {
      const companyIdsArray = Array.from(companyIds);
      const placeholders = companyIdsArray.map(() => '?').join(',');
      const companiesResult = await query(`
        SELECT id, company_name
        FROM brands
        WHERE id IN (${placeholders})
      `, companyIdsArray) as any[];

      companiesResult.forEach(company => {
        companiesMap.set(company.id, company.company_name);
      });
    }

    // 格式化短剧数据（修复字段引用）
    const formattedDrama = {
      id: drama.id,
      title: drama.title,
      cover: drama.cover,
      tags: drama.tags ? JSON.parse(drama.tags) : [],
      fundingGoal: parseFloat(drama.funding_goal) || 0,
      currentFunding: parseFloat(drama.current_funding) || 0,
      remainingDays: parseInt(drama.remaining_days) || 0,
      description: drama.description,
      isOnline: drama.is_online,
      director: drama.director,
      scriptwriter: drama.scriptwriter,
      producer: drama.producer,
      cast: drama.cast ? JSON.parse(drama.cast) : [],
      episodes: parseInt(drama.episodes) || 0,
      episodeLength: parseInt(drama.episode_length) || 0,
      targetPlatform: drama.target_platform ? JSON.parse(drama.target_platform) : [],
      expectedReleaseDate: drama.expected_release_date,
      expectedReturn: parseFloat(drama.expected_return) || 0,
      minInvestment: parseFloat(drama.min_investment) || 0,
      fundingShare: parseFloat(drama.funding_share) || 100,
      projectedViews: drama.projected_views,
      roi: parseFloat(drama.roi) || 0,
      schedulePreProduction: drama.schedule_pre_production,
      scheduleFilming: drama.schedule_filming,
      schedulePostProduction: drama.schedule_post_production,
      scheduleRelease: drama.expected_release_date,
      // 制作团队信息（合并演员和公司详细信息）
      productionTeam: {
        productionCompany: mergeCompanyInfo(drama.production_company, companiesMap),
        coProductionCompany: mergeCompanyInfo(drama.co_production_company, companiesMap),
        executiveProducer: mergeActorInfo(drama.executive_producer, actorsMap),
        coExecutiveProducer: mergeActorInfo(drama.co_executive_producer, actorsMap),
        chiefProducer: mergeActorInfo(drama.chief_producer, actorsMap),
        producer: mergeActorInfo(drama.producer, actorsMap),
        coProducer: mergeActorInfo(drama.co_producer, actorsMap),
        director: mergeActorInfo(drama.director, actorsMap),
        scriptwriter: mergeActorInfo(drama.scriptwriter, actorsMap),
        supervisor: mergeActorInfo(drama.supervisor, actorsMap),
        coordinator: mergeActorInfo(drama.coordinator, actorsMap)
      },
      // 同时提供嵌套的schedule对象供前端使用
      schedule: {
        preProduction: drama.schedule_pre_production,
        filming: drama.schedule_filming,
        postProduction: drama.schedule_post_production,
        release: drama.expected_release_date
      },
      // 从additional_info表获取的数据
      confirmedResources: additionalInfoResult.length > 0 && additionalInfoResult[0].confirmed_resources
        ? JSON.parse(additionalInfoResult[0].confirmed_resources) : [],
      riskManagement: additionalInfoResult.length > 0 && additionalInfoResult[0].risk_management
        ? JSON.parse(additionalInfoResult[0].risk_management) : [],

      // 投资权益档位信息
      investmentTiers: investmentTiersResult.map(tier => ({
        id: tier.id,
        tierName: tier.tier_name,
        minAmount: parseFloat(tier.min_amount),
        maxAmount: tier.max_amount ? parseFloat(tier.max_amount) : null,
        benefits: tier.benefits,
        returnRate: tier.return_rate ? parseFloat(tier.return_rate) : null,
        limitedQuantity: tier.limited_quantity,
        soldQuantity: tier.sold_quantity || 0,
        sortOrder: tier.sort_order || 0,
        isActive: tier.is_active === 1
      })),

      // 营业执照信息
      businessLicense: businessLicenseInfo,

      createdAt: drama.created_at,
      updatedAt: drama.updated_at,
      // 计算募资进度
      fundingProgress: drama.funding_goal > 0 ? Math.round((parseFloat(drama.current_funding) / parseFloat(drama.funding_goal)) * 100) : 0,

      // 投资人信息
      investors: {
        totalCount: totalInvestors,
        list: investorsResult.map(investor => ({
          id: investor.id,
          username: investor.username,
          realName: investor.real_name,
          investmentAmount: parseFloat(investor.investment_amount),
          investmentDate: investor.investment_date
        }))
      },

      // 格式化素材列表
      materials: materialsResult.map((material: any) => ({
        id: material.id,
        dramaId: material.drama_id,
        title: material.title,
        url: material.url,
        thumbnail: material.thumbnail,
        type: material.type,
        sortOrder: material.sort_order,
        createdAt: material.created_at,
        updatedAt: material.updated_at
      })),

      // 格式化文档列表
      documents: documentsResult.map((document: any) => ({
        id: document.id,
        dramaId: document.drama_id,
        name: document.name,
        fileUrl: document.file_url,
        fileType: document.file_type,
        fileSize: document.file_size,
        createdAt: document.created_at,
        updatedAt: document.updated_at
      }))
    };

    return {
      success: true,
      data: formattedDrama
    };

  } catch (error: any) {
    console.error('获取公开短剧详情失败', {
      error: error.message,
      dramaId: getRouterParam(event, 'id'),
      ip: getClientIP(event) || 'unknown'
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
