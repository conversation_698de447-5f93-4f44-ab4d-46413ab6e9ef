<template>
  <div class="admin-card">
    <template v-if="documents && documents.length > 0">
      <div class="space-y-4">
        <div
          v-for="document in documents"
          :key="document.id"
          class="border border-gray-200 rounded-lg p-4"
        >
          <a-form
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
            layout="horizontal"
          >
            <a-form-item label="文档名称" class="form-item-visible">
              <a-input
                :value="document.name || ''"
                disabled
                class="input-disabled"
              />
            </a-form-item>

            <a-form-item label="文件类型" class="form-item-visible">
              <a-input
                :value="document.fileType || ''"
                disabled
                class="input-disabled"
                style="width: 120px"
              />
            </a-form-item>

            <a-form-item label="文件大小" class="form-item-visible">
              <a-input
                :value="document.fileSize || ''"
                disabled
                class="input-disabled"
                style="width: 120px"
              />
            </a-form-item>

            <a-form-item label="文件链接" class="form-item-visible">
              <div class="flex items-center space-x-2">
                <a-input
                  :value="document.fileUrl || ''"
                  disabled
                  class="input-disabled flex-1"
                />
                <a-button
                  type="link"
                  size="small"
                  @click="downloadDocument(document)"
                >
                  下载
                </a-button>
              </div>
            </a-form-item>

            <a-form-item label="创建时间" class="form-item-visible">
              <a-input
                :value="formatDate(document.createdAt || '')"
                disabled
                class="input-disabled"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </template>

    <template v-else>
      <div class="text-center py-8 text-gray-500">
        暂无剧本文档
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { Form as AForm, FormItem as AFormItem, Input as AInput, Button as AButton, message } from 'ant-design-vue';

interface Document {
  id: number;
  name: string;
  fileUrl: string;
  fileType?: string;
  fileSize?: string;
  createdAt?: string;
}

interface Props {
  documents: Document[];
}

const props = defineProps<Props>();

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

// 下载文档
const downloadDocument = (document: Document) => {
  if (!document.fileUrl) {
    message.error('文档链接不存在');
    return;
  }

  // 创建下载链接
  const link = document.createElement('a');
  link.href = document.fileUrl;
  link.download = document.name;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  message.success('开始下载文档');
};
</script>

<style scoped>
.admin-card {
  @apply bg-white p-6 rounded-lg shadow-sm;
}

.form-item-visible {
  @apply mb-4;
}

.input-disabled {
  @apply bg-gray-50 text-gray-700;
}

.input-disabled:disabled {
  @apply bg-gray-50 text-gray-700 cursor-default;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

:deep(.ant-form-item-label) {
  @apply font-medium;
}
</style>
