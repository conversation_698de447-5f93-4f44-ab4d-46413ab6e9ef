<script setup>
import { ref, computed, reactive, onMounted, onUnmounted, watch } from 'vue'
import { RouterLink } from 'vue-router'
import { getPublicFunds, getFundTypes, getRiskLevels, getPeriodTypes } from '../../api/public/fundApi'
import { formatCurrency, calculateProgress } from '../../api/utils'
import { formatExpectedReturnToDescription, getReturnDescriptionColor } from '../../utils/fundUtils'

// 加载状态
const loading = ref(false)
const error = ref(null)

// 数据状态
const funds = ref([])
const filterOptions = reactive({
  type: [],
  risk: [],
  period: []
})

// 筛选条件
const filters = reactive({
  type: [], // 基金类型：股权型/债权型/混合型
  risk: [], // 风险等级：R1~R5
  minInvestment: 0, // 起投金额：最小值
  period: [], // 封闭期：≤1年 / 1-3年 / >3年
  search: '' // 搜索关键词
})

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 已选中的基金进行对比
const selectedForComparison = ref([])

// 下拉框显示状态
const dropdownStates = reactive({
  type: false,
  risk: false,
  period: false
})

// 初始化筛选条件选项
const initFilterOptions = async () => {
  try {
    // 这里使用Promise.all同时加载所有筛选选项
    const [types, risks, periods] = await Promise.all([
      getFundTypes(),
      getRiskLevels(),
      getPeriodTypes()
    ])
    
    filterOptions.type = types
    filterOptions.risk = risks
    filterOptions.period = periods
  } catch (err) {
    console.error('加载筛选选项失败:', err)
    error.value = '加载筛选选项失败，请刷新页面重试'
  }
}

// 加载基金数据
const loadFunds = async () => {
  loading.value = true
  error.value = null
  
  try {
    // 构建筛选参数
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    // 添加类型筛选
    if (filters.type && filters.type.length > 0) {
      params.type = filters.type
    }
    
    // 添加风险筛选
    if (filters.risk && filters.risk.length > 0) {
      params.risk = filters.risk
    }
    
    // 添加封闭期筛选
    if (filters.period && filters.period.length > 0) {
      params.period = filters.period
    }
    
    // 添加起投金额筛选
    if (filters.minInvestment > 0) {
      params.minInvestment = filters.minInvestment
    }

    // 添加搜索关键词
    if (filters.search && filters.search.trim()) {
      params.search = filters.search.trim()
    }

    // 构建URL参数
    const baseURL = 'http://localhost:3001'
    const urlParams = new URLSearchParams()

    // 添加基础参数
    urlParams.append('page', params.page)
    urlParams.append('pageSize', params.pageSize)

    // 添加筛选参数
    if (params.type && params.type.length > 0) {
      urlParams.append('type', params.type.join(','))
    }
    if (params.risk && params.risk.length > 0) {
      urlParams.append('risk', params.risk.join(','))
    }
    if (params.period && params.period.length > 0) {
      urlParams.append('period', params.period.join(','))
    }
    if (params.minInvestment > 0) {
      urlParams.append('minInvestment', params.minInvestment)
    }
    if (params.search) {
      urlParams.append('search', params.search)
    }

    const url = `${baseURL}/api/funds?${urlParams.toString()}`
    console.log('API请求URL:', url)

    const response = await fetch(url)
    const data = await response.json()

    if (data.success && data.data) {
      funds.value = data.data.list || []
      pagination.total = data.data.pagination?.total || 0
    } else {
      funds.value = []
      pagination.total = 0
    }
  } catch (err) {
    console.error('加载基金数据失败:', err)
    error.value = '加载基金数据失败，请刷新页面重试'
  } finally {
    loading.value = false
  }
}

// 根据筛选条件过滤基金
const filteredFunds = computed(() => {
  return funds.value
})

// 切换基金对比选择
const toggleFundSelection = (fundId) => {
  const index = selectedForComparison.value.indexOf(fundId)
  if (index === -1) {
    // 如果已经选择了4个，不能再添加
    if (selectedForComparison.value.length >= 4) {
      return
    }
    selectedForComparison.value.push(fundId)
  } else {
    selectedForComparison.value.splice(index, 1)
  }
}

// 监听筛选条件变化，自动加载数据
watch(() => [...filters.type], () => {
  console.log('基金类型筛选变化，自动重新加载数据')
  pagination.page = 1
  loadFunds()
}, { deep: true })

watch(() => [...filters.risk], () => {
  console.log('风险等级筛选变化，自动重新加载数据')
  pagination.page = 1
  loadFunds()
}, { deep: true })

watch(() => [...filters.period], () => {
  console.log('封闭期筛选变化，自动重新加载数据')
  pagination.page = 1
  loadFunds()
}, { deep: true })

watch(() => filters.minInvestment, (newVal, oldVal) => {
  // 添加防抖，避免滑动条拖动过程中频繁请求
  if (minInvestmentTimer) clearTimeout(minInvestmentTimer)
  minInvestmentTimer = setTimeout(() => {
    console.log('起投金额筛选变化，自动重新加载数据', newVal)
    pagination.page = 1
    loadFunds()
  }, 500) // 500ms防抖
})

// 监听搜索关键词变化
watch(() => filters.search, (newVal, oldVal) => {
  // 添加防抖，避免输入过程中频繁请求
  if (searchTimer) clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    console.log('搜索关键词变化，自动重新加载数据', newVal)
    pagination.page = 1
    loadFunds()
  }, 300) // 300ms防抖
})

// 用于起投金额滑动条的防抖
let minInvestmentTimer = null
// 用于搜索的防抖
let searchTimer = null

// 清空筛选条件
const clearFilters = () => {
  console.log('清空筛选条件')
  filters.type = []
  filters.risk = []
  filters.minInvestment = 0
  filters.period = []
  filters.search = ''

  // 重新加载数据
  pagination.page = 1
  loadFunds()
}

// 收藏基金
const toggleFavorite = (fundId) => {
  const fund = funds.value.find(f => f.id === fundId)
  if (fund) {
    fund.isFavorite = !fund.isFavorite
  }
}

// 获取风险等级对应的颜色
const getRiskLevelColor = (risk) => {
  const riskItem = filterOptions.risk.find(r => r.id === risk)
  return riskItem ? riskItem.color : 'bg-gray-500'
}

// 获取封闭期文本
const getPeriodText = (periodId) => {
  const period = filterOptions.period.find(p => p.id === periodId)
  return period ? period.label : ''
}

// 切换下拉框显示状态
const toggleDropdown = (type) => {
  dropdownStates[type] = !dropdownStates[type]
  // 关闭其他下拉框
  Object.keys(dropdownStates).forEach(key => {
    if (key !== type) {
      dropdownStates[key] = false
    }
  })
}

// 切换选项选择
const toggleOption = (filterType, optionId) => {
  const index = filters[filterType].indexOf(optionId)
  if (index === -1) {
    filters[filterType].push(optionId)
  } else {
    filters[filterType].splice(index, 1)
  }
}

// 获取已选择选项的显示文本
const getSelectedText = (filterType) => {
  const selectedCount = filters[filterType].length
  if (selectedCount === 0) {
    switch(filterType) {
      case 'type': return '请选择基金类型'
      case 'risk': return '请选择风险等级'
      case 'period': return '请选择封闭期'
      default: return '请选择'
    }
  }
  return `已选${selectedCount}项`
}

// 点击外部关闭下拉框
const closeDropdowns = () => {
  Object.keys(dropdownStates).forEach(key => {
    dropdownStates[key] = false
  })
}

// 页面加载
onMounted(async () => {
  await initFilterOptions()
  loadFunds()

  // 添加全局点击事件监听器
  document.addEventListener('click', closeDropdowns)
})

// 组件卸载时移除事件监听器
onUnmounted(() => {
  document.removeEventListener('click', closeDropdowns)
})
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-6">
    <div class="container mx-auto px-4">
      
      <!-- 筛选器组件 -->
      <div class="bg-white rounded shadow-sm p-4 mb-6">
        <!-- 筛选条件行 -->
        <div class="grid grid-cols-1 lg:grid-cols-12 gap-3 items-center" @click.stop>
          <!-- 搜索框 -->
          <div class="lg:col-span-3">
            <div class="relative">
              <input
                type="text"
                v-model="filters.search"
                placeholder="基金名称/代码"
                class="w-full px-3 py-2 text-sm border border-gray-200 rounded focus:outline-none focus:border-primary"
              >
              <svg class="w-4 h-4 text-gray-400 absolute right-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          <!-- 基金类型筛选 -->
          <div class="lg:col-span-2">
            <div class="relative">
              <div
                @click.stop="toggleDropdown('type')"
                class="w-full px-3 py-2 text-sm border border-gray-200 rounded focus:outline-none focus:border-primary bg-white cursor-pointer flex items-center justify-between"
              >
                <span :class="{ 'text-gray-400': filters.type.length === 0 }">
                  {{ getSelectedText('type') }}
                </span>
                <svg class="w-4 h-4 text-gray-400 transition-transform" :class="{ 'rotate-180': dropdownStates.type }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              <!-- 下拉选项 -->
              <div v-if="dropdownStates.type" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-b shadow-lg z-10 max-h-48 overflow-y-auto">
                <div
                  v-for="option in filterOptions.type"
                  :key="option.id"
                  @click.stop="toggleOption('type', option.id)"
                  class="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center"
                >
                  <input
                    type="checkbox"
                    :checked="filters.type.includes(option.id)"
                    class="mr-2 text-primary"
                    readonly
                  >
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 风险等级筛选 -->
          <div class="lg:col-span-2">
            <div class="relative">
              <div
                @click.stop="toggleDropdown('risk')"
                class="w-full px-3 py-2 text-sm border border-gray-200 rounded focus:outline-none focus:border-primary bg-white cursor-pointer flex items-center justify-between"
              >
                <span :class="{ 'text-gray-400': filters.risk.length === 0 }">
                  {{ getSelectedText('risk') }}
                </span>
                <svg class="w-4 h-4 text-gray-400 transition-transform" :class="{ 'rotate-180': dropdownStates.risk }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              <!-- 下拉选项 -->
              <div v-if="dropdownStates.risk" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-b shadow-lg z-10 max-h-48 overflow-y-auto">
                <div
                  v-for="option in filterOptions.risk"
                  :key="option.id"
                  @click.stop="toggleOption('risk', option.id)"
                  class="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center"
                >
                  <input
                    type="checkbox"
                    :checked="filters.risk.includes(option.id)"
                    class="mr-2 text-primary"
                    readonly
                  >
                  <span>{{ option.id }} ({{ option.label }})</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 封闭期筛选 -->
          <div class="lg:col-span-2">
            <div class="relative">
              <div
                @click.stop="toggleDropdown('period')"
                class="w-full px-3 py-2 text-sm border border-gray-200 rounded focus:outline-none focus:border-primary bg-white cursor-pointer flex items-center justify-between"
              >
                <span :class="{ 'text-gray-400': filters.period.length === 0 }">
                  {{ getSelectedText('period') }}
                </span>
                <svg class="w-4 h-4 text-gray-400 transition-transform" :class="{ 'rotate-180': dropdownStates.period }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              <!-- 下拉选项 -->
              <div v-if="dropdownStates.period" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-b shadow-lg z-10 max-h-48 overflow-y-auto">
                <div
                  v-for="option in filterOptions.period"
                  :key="option.id"
                  @click.stop="toggleOption('period', option.id)"
                  class="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center"
                >
                  <input
                    type="checkbox"
                    :checked="filters.period.includes(option.id)"
                    class="mr-2 text-primary"
                    readonly
                  >
                  <span>{{ option.label }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 起投金额筛选 -->
          <div class="lg:col-span-2">
            <div class="flex items-center">
              <span class="text-sm text-gray-600 mr-2">0</span>
              <div class="flex-1 mx-1">
                <input
                  type="range"
                  v-model.number="filters.minInvestment"
                  min="0"
                  max="5000000"
                  step="100000"
                  class="w-full accent-primary"
                >
              </div>
              <span class="text-sm text-gray-600 ml-2">500万</span>
            </div>
            <div class="text-sm text-gray-600 mt-1 text-center">
              {{ formatCurrency(filters.minInvestment) }}元
            </div>
          </div>

          <!-- 重置按钮 -->
          <div class="lg:col-span-1 flex justify-end">
            <button
              @click="clearFilters"
              class="px-4 py-2 text-gray-500 hover:text-primary text-sm border border-gray-200 rounded hover:border-primary transition-colors flex items-center"
            >
              <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              重置
            </button>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="bg-white rounded shadow-sm p-12 mb-6 flex justify-center">
        <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
      </div>

      <!-- 错误信息 -->
      <div v-else-if="error" class="bg-white rounded shadow-sm p-8 mb-6 text-center">
        <svg class="w-16 h-16 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <p class="text-xl font-medium text-gray-900 mb-2">出错了</p>
        <p class="text-base text-gray-600">{{ error }}</p>
        <button @click="loadFunds" class="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors">
          重试
        </button>
      </div>
      
      <!-- 基金列表 -->
      <div v-else-if="filteredFunds.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <div
          v-for="fund in filteredFunds"
          :key="fund.id"
          class="bg-white rounded border border-gray-200 overflow-hidden hover:border-gray-300 transition-colors"
        >
          <!-- 基金头部 -->
          <div class="p-4">
            <div class="flex justify-between items-start mb-2">
              <h3 class="text-lg font-semibold leading-tight">{{ fund.title }}</h3>
              <div class="flex items-center space-x-2">
                <span class="px-2 py-1 text-sm rounded text-white font-medium" :class="getRiskLevelColor(fund.risk)">{{ fund.risk }}</span>
                <button @click="toggleFavorite(fund.id)" class="text-gray-400 hover:text-yellow-500">
                  <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" :class="{ 'text-yellow-500 fill-yellow-500': fund.isFavorite }">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                </button>
              </div>
            </div>
            <div class="text-gray-500 text-sm mb-3">{{ fund.id }}</div>

            <!-- 募集进度 -->
            <div class="mb-4">
              <div class="flex justify-between text-sm mb-2">
                <span>已募集</span>
                <span>{{ calculateProgress(fund.raisedAmount, fund.targetSize) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                <div
                  class="bg-indigo-500 h-2.5 rounded-full"
                  :style="{ width: calculateProgress(fund.raisedAmount, fund.targetSize) + '%' }"
                ></div>
              </div>
              <div class="flex justify-between text-sm">
                <span>{{ formatCurrency(fund.raisedAmount) }}</span>
                <span class="text-gray-500">目标 {{ formatCurrency(fund.targetSize) }}</span>
              </div>
            </div>

            <!-- 基金信息 -->
            <div class="space-y-2 mb-4">
              <div class="flex justify-between">
                <div class="text-gray-500 text-sm">预期年化</div>
                <div class="font-medium text-base" :class="getReturnDescriptionColor(formatExpectedReturnToDescription(fund.expectedReturn))">
                  {{ formatExpectedReturnToDescription(fund.expectedReturn) }}
                </div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray-500 text-sm">最低起投</div>
                <div class="font-medium text-base">{{ formatCurrency(fund.minInvestment) }}元</div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray-500 text-sm">最低持有期</div>
                <div class="font-medium text-base">{{ fund.minHoldingPeriod || 24 }}个月</div>
              </div>
              <div class="flex justify-between">
                <div class="text-gray-500 text-sm">封闭期</div>
                <div class="font-medium text-base">{{ getPeriodText(fund.period) }}</div>
              </div>
            </div>

            <!-- 风险提示和操作 -->
            <div class="border-t border-gray-100 mt-4 pt-3 flex items-center justify-between">
              <div class="flex items-center text-yellow-600 text-sm">
                <svg class="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                <span>风险提示</span>
              </div>
            </div>

            <!-- 按钮操作区 -->
            <div class="border-t border-gray-100 mt-3 pt-4 flex items-center justify-between">
              <label class="inline-flex items-center text-gray-600 cursor-pointer">
                <input
                  type="checkbox"
                  :checked="selectedForComparison.includes(fund.id)"
                  @change="toggleFundSelection(fund.id)"
                  class="form-checkbox h-4 w-4 text-primary rounded"
                >
                <span class="ml-2 text-sm">加入对比</span>
              </label>
              <RouterLink
                :to="`/funds/${fund.id}`"
                class="inline-block px-6 py-2 bg-primary text-white rounded hover:bg-primary-dark focus:bg-primary-dark active:bg-primary-dark transition-colors text-sm font-medium"
              >
                查看详情
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="bg-white rounded border border-gray-200 p-8 mb-6 text-center">
        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-lg font-medium text-gray-900 mb-2">暂无符合条件的基金</p>
        <p class="text-gray-600 mb-4">请尝试调整筛选条件后重新查询</p>
        <button @click="clearFilters" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors">
          清空筛选条件
        </button>
      </div>

      <!-- 分页区域 -->
      <div v-if="!loading && !error && filteredFunds.length > 0" class="flex justify-center my-6">
        <div class="flex space-x-2">
          <button 
            @click="pagination.page > 1 && (pagination.page--, loadFunds())" 
            class="px-3 py-1 rounded border"
            :class="pagination.page > 1 ? 'text-primary border-primary' : 'text-gray-400 border-gray-200 cursor-not-allowed'"
          >
            上一页
          </button>
          <div class="px-3 py-1 text-gray-700">
            第 {{ pagination.page }} 页 / 共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
          </div>
          <button
            @click="pagination.page < Math.ceil(pagination.total / pagination.pageSize) && (pagination.page++, loadFunds())"
            class="px-3 py-1 rounded border"
            :class="pagination.page < Math.ceil(pagination.total / pagination.pageSize) ? 'text-primary border-primary' : 'text-gray-400 border-gray-200 cursor-not-allowed'"
          >
            下一页
          </button>
        </div>
      </div>
      
      <!-- 对比浮层 -->
      <div 
        v-if="selectedForComparison.length > 0" 
        class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 z-50"
      >
        <div class="container mx-auto">
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <span class="font-medium mr-4">已选择 {{ selectedForComparison.length }} 个基金进行对比</span>
              <div class="flex space-x-3">
                <div 
                  v-for="fundId in selectedForComparison" 
                  :key="fundId" 
                  class="flex items-center bg-gray-100 rounded-full px-3 py-1"
                >
                  <span class="text-sm truncate max-w-[120px]">{{ funds.value.find(f => f.id === fundId)?.title }}</span>
                  <button @click="toggleFundSelection(fundId)" class="ml-1 text-gray-500">
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex space-x-4">
              <button 
                @click="selectedForComparison = []" 
                class="text-gray-500 hover:text-gray-700"
              >
                清空
              </button>
              <RouterLink 
                :to="`/funds/compare?ids=${selectedForComparison.join(',')}`" 
                class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                开始对比
              </RouterLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.text-primary {
  color: #8667F0;
}
.bg-primary {
  background-color: #8667F0;
}
.border-primary {
  border-color: #8667F0;
}
.bg-primary-dark {
  background-color: #6039E4;
}
.hover\:bg-primary-dark:hover {
  background-color: #6039E4;
}
.accent-primary {
  accent-color: #8667F0;
}
</style> 