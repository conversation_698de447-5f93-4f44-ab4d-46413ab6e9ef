<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 新闻数据（与HomeView中的数据保持一致）
const newsData = ref([
  {
    id: 1,
    title: "短剧市场迎来爆发式增长，2024年预计突破500亿规模",
    summary: "据最新行业报告显示，短剧市场在2024年预计将突破500亿元规模，同比增长超过150%。微短剧作为新兴内容形态，正在重塑整个影视娱乐产业格局。",
    category: "行业动态",
    author: "剧投投研究院",
    publishDate: "2024-08-15",
    readCount: 15420,
    coverImage: "https://via.placeholder.com/800x450/3B82F6/FFFFFF?text=短剧市场分析",
    content: `
# 短剧市场迎来爆发式增长，2024年预计突破500亿规模

## 市场概况

据剧投投研究院最新发布的《2024年中国短剧市场发展报告》显示，短剧市场正在经历前所未有的爆发式增长。预计2024年整体市场规模将突破500亿元人民币，相比2023年的200亿元规模，同比增长率将超过150%。

这一增长数据不仅反映了短剧内容的受众接受度快速提升，更体现了整个产业链的日趋成熟。从内容制作、平台分发到商业变现，短剧产业已经形成了相对完整的生态体系。

## 增长驱动因素

### 1. 用户消费习惯的转变

随着移动互联网的深度普及和5G技术的广泛应用，用户的内容消费习惯正在发生根本性转变。短剧以其"短平快"的特点，完美契合了现代人碎片化的时间使用习惯。

数据显示，短剧的平均单集时长在1-3分钟之间，这种时长设计既能保证故事的完整性，又能最大化利用用户的碎片时间。相比传统长剧动辄40-60分钟的单集时长，短剧在用户时间争夺战中具有明显优势。

### 2. 制作成本的优化

短剧制作相比传统影视剧具有明显的成本优势。一部优质短剧的制作成本通常在50万-200万元之间，而传统电视剧的制作成本往往需要数千万甚至上亿元。

这种成本优势使得更多的创作者和投资方能够参与到内容制作中来，极大地丰富了市场供给。同时，较低的制作门槛也为创新内容的涌现提供了土壤。

### 3. 商业模式的创新

短剧行业在商业模式上展现出了强大的创新能力。除了传统的广告收入模式外，付费观看、会员订阅、IP衍生品开发等多元化变现方式正在快速发展。

特别是付费观看模式，已经成为短剧平台的重要收入来源。数据显示，愿意为优质短剧内容付费的用户比例已经超过40%，这一比例还在持续上升。

## 平台竞争格局

### 主流平台表现

目前，短剧市场的主要玩家包括抖音、快手、微博、B站等传统短视频平台，以及专门的短剧平台如剧投投、微短剧等。各平台都在加大对短剧内容的投入力度。

抖音作为短视频领域的头部平台，在短剧领域也展现出了强劲的竞争力。其"抖音短剧"频道已经聚集了大量优质内容，日活跃用户数超过5000万。

快手则通过"快手小剧场"等产品，在下沉市场建立了稳固的用户基础。其短剧内容更加贴近三四线城市用户的喜好，形成了差异化竞争优势。

### 专业平台的崛起

除了传统短视频平台外，专门的短剧平台也在快速崛起。剧投投作为专业的短剧投资与制作平台，通过"投资+制作+分发"的全产业链模式，为行业提供了新的发展思路。

这些专业平台的优势在于对短剧内容的深度理解和专业化运营能力。它们不仅能够为创作者提供更好的创作环境，还能为投资者提供更加透明和专业的投资服务。

## 内容趋势分析

### 题材多元化

短剧的题材正在呈现多元化发展趋势。从最初的都市情感、霸道总裁等传统题材，逐步扩展到悬疑推理、科幻奇幻、历史古装等多个领域。

这种题材的多元化不仅满足了不同用户群体的需求，也为创作者提供了更大的创作空间。同时，垂直化的内容定位也有助于平台建立更加精准的用户画像。

### 制作水准提升

随着市场竞争的加剧，短剧的制作水准正在快速提升。从剧本创作、演员选择到后期制作，各个环节都在向专业化、精品化方向发展。

许多短剧作品在视觉效果、音响效果等技术层面已经达到了电影级别的水准。这种制作水准的提升不仅提高了用户的观看体验，也为短剧行业赢得了更多的社会认可。

## 投资机会与挑战

### 投资机会

短剧市场的快速发展为投资者提供了丰富的投资机会。从内容制作、技术服务到平台运营，产业链的各个环节都存在投资价值。

特别是在内容制作领域，优质IP的稀缺性使得具有创新能力的制作团队成为投资热点。同时，技术服务商也在这一轮发展中获得了大量机会，包括视频制作工具、数据分析服务、营销推广等。

### 面临挑战

尽管市场前景广阔，但短剧行业也面临着一些挑战。首先是内容同质化问题，随着入局者的增多，内容创新的难度在不断加大。

其次是监管政策的不确定性。作为新兴业态，短剧行业的监管框架还在不断完善中，政策变化可能对行业发展产生重要影响。

最后是人才短缺问题。短剧行业需要既懂传统影视制作又了解互联网运营的复合型人才，这类人才目前还比较稀缺。

## 未来展望

展望未来，短剧市场仍将保持高速增长态势。预计到2025年，市场规模有望突破800亿元。同时，随着技术的不断进步和商业模式的持续创新，短剧行业将迎来更加广阔的发展空间。

对于投资者而言，现在正是布局短剧市场的最佳时机。通过选择优质的项目和团队，投资者不仅能够获得可观的财务回报，还能参与到这一新兴产业的发展进程中来。

剧投投作为行业领先的短剧投资平台，将继续为投资者和创作者搭建桥梁，推动整个行业的健康发展。我们相信，在各方的共同努力下，短剧行业必将迎来更加辉煌的明天。
    `
  },
  {
    id: 2,
    title: "政策利好频出，短剧行业迎来规范化发展新机遇",
    summary: "国家广电总局近期发布多项政策文件，为短剧行业发展提供了明确的政策指引。新政策在内容审核、版权保护、商业模式等方面都有重要突破。",
    category: "政策法规",
    author: "政策解读小组",
    publishDate: "2024-08-12",
    readCount: 12350,
    coverImage: "https://via.placeholder.com/800x450/10B981/FFFFFF?text=政策解读",
    content: `
# 政策利好频出，短剧行业迎来规范化发展新机遇

## 政策背景

2024年8月，国家广播电视总局连续发布了《关于进一步规范网络微短剧创作生产的通知》、《网络视听节目内容审核通则（2024版）》等重要政策文件，为短剧行业的规范化发展指明了方向。

这些政策的出台，标志着监管部门对短剧这一新兴业态的重视程度不断提升，同时也为行业的健康发展提供了重要保障。对于从业者和投资者而言，理解和把握这些政策变化，将是未来成功的关键。

## 主要政策内容解读

### 1. 内容审核标准明确化

新版审核通则对短剧内容的审核标准进行了明确规定，主要包括以下几个方面：

**价值导向要求**：短剧内容必须坚持正确的价值导向，传播正能量，弘扬社会主义核心价值观。这一要求虽然看似严格，但实际上为优质内容的创作提供了明确的方向指引。

**题材限制规范**：对于涉及历史、军事、医疗等敏感题材的短剧，审核标准更加严格。创作者需要确保内容的真实性和准确性，避免误导观众。

**技术标准提升**：在视频质量、音频效果等技术层面，新政策也提出了更高要求。这将推动整个行业的制作水准向专业化方向发展。

### 2. 版权保护机制完善

新政策在版权保护方面有了重大突破，主要体现在：

**原创内容保护**：建立了更加完善的原创内容保护机制，对于抄袭、洗稿等行为将面临更严厉的处罚。这为原创内容创作者提供了更好的保护。

**IP开发规范**：对于基于小说、漫画等IP改编的短剧，明确了版权授权的具体要求和流程。这将有助于减少版权纠纷，促进IP产业的健康发展。

**平台责任明确**：各大平台需要建立完善的版权保护机制，对于侵权内容要及时处理。这将推动平台方加大对版权保护的投入。

### 3. 商业模式规范化

在商业模式方面，新政策也提出了明确要求：

**付费模式规范**：对于付费观看模式，要求平台必须明确告知用户付费规则，不得进行误导性宣传。同时，要保障用户的知情权和选择权。

**广告投放标准**：对于短剧中的广告植入，要求必须明确标识，不得影响用户的正常观看体验。这将推动广告投放的规范化发展。

**数据安全要求**：平台需要建立完善的用户数据保护机制，确保用户隐私安全。这对于平台的技术能力提出了更高要求。

## 政策影响分析

### 对创作者的影响

**积极影响**：
- 明确的审核标准有助于创作者更好地把握内容方向
- 版权保护机制的完善为原创内容提供了更好的保障
- 规范化的市场环境有利于优质内容的脱颖而出

**挑战与应对**：
- 审核标准的提高可能会增加内容创作的难度
- 创作者需要更加注重内容的质量和价值导向
- 建议创作者加强对政策的学习和理解，确保内容合规

### 对平台方的影响

**合规成本上升**：
新政策要求平台建立更加完善的内容审核、版权保护、用户数据保护等机制，这将导致平台的合规成本上升。

**竞争格局变化**：
具有更强技术实力和合规能力的平台将在竞争中占据优势，而一些小平台可能面临更大的生存压力。

**商业模式优化**：
平台需要在政策框架内优化商业模式，寻找新的增长点和盈利模式。

### 对投资者的影响

**投资门槛提高**：
随着行业规范化程度的提升，投资门槛也相应提高。投资者需要更加关注项目的合规性和可持续性。

**投资机会优化**：
政策的明确化为投资者提供了更加清晰的投资方向，有助于降低投资风险。

**长期价值凸显**：
规范化的发展环境有利于行业的长期健康发展，为投资者创造更大的长期价值。

## 行业应对策略

### 内容制作方面

**加强内容策划**：
制作方需要在项目策划阶段就充分考虑政策要求，确保内容方向的正确性。

**提升制作水准**：
随着技术标准的提高，制作方需要加大对技术设备和人才的投入，提升整体制作水准。

**建立合规体系**：
建立完善的内容合规审查体系，确保每一部作品都能够通过审核。

### 平台运营方面

**技术升级**：
平台需要加大对技术系统的投入，建立更加智能化的内容审核、版权保护、用户管理系统。

**人才培养**：
加强对审核人员、技术人员的培训，提升团队的专业能力。

**合作共赢**：
与监管部门、行业协会等建立良好的合作关系，共同推动行业的健康发展。

### 投资策略调整

**项目筛选标准**：
投资者需要将合规性作为项目筛选的重要标准，优先投资具有良好合规记录的项目。

**风险管理**：
建立更加完善的风险管理体系，对政策风险进行充分评估和防范。

**长期布局**：
在政策明确的背景下，投资者可以进行更加长期的战略布局。

## 未来发展趋势

### 行业集中度提升

随着政策门槛的提高，行业集中度将进一步提升。具有更强实力的头部企业将占据更大的市场份额，而一些实力较弱的企业可能面临淘汰。

### 内容质量持续提升

在政策引导下，整个行业将更加注重内容质量的提升。低质量、同质化的内容将逐步被市场淘汰，优质原创内容将获得更大的发展空间。

### 商业模式创新

在政策框架内，行业将探索更多创新的商业模式。除了传统的广告和付费模式外，IP衍生品开发、线下活动等新模式也将得到发展。

### 国际化发展

随着国内市场的规范化发展，优质的短剧内容也将加速走向国际市场，为中国文化的海外传播贡献力量。

## 结语

政策的出台虽然在短期内可能会给行业带来一定的调整压力，但从长期来看，这些政策为短剧行业的健康发展奠定了坚实基础。

对于从业者而言，关键是要积极拥抱政策变化，在合规的前提下寻找发展机遇。对于投资者而言，政策的明确化降低了投资风险，为长期投资提供了更好的环境。

剧投投将继续密切关注政策动态，为平台上的创作者和投资者提供最新的政策解读和合规指导，共同推动短剧行业的规范化、专业化发展。

我们相信，在政策的正确引导下，短剧行业必将迎来更加光明的未来，为广大观众提供更多优质的内容，为投资者创造更大的价值。
    `
  },
  {
    id: 3,
    title: "剧投投平台Q2投资报告：短剧项目平均回报率达180%",
    summary: "剧投投平台发布2024年第二季度投资报告，平台上短剧项目平均投资回报率达到180%，其中《都市情缘》系列项目回报率更是高达350%。",
    category: "投资资讯",
    author: "剧投投数据中心",
    publishDate: "2024-08-10",
    readCount: 18750,
    coverImage: "https://via.placeholder.com/800x450/8B5CF6/FFFFFF?text=投资报告",
    content: `
# 剧投投平台Q2投资报告：短剧项目平均回报率达180%

## 报告概述

剧投投平台今日发布《2024年第二季度短剧投资报告》，数据显示，平台上短剧项目的平均投资回报率达到180%，远超传统影视投资的平均水平。这一亮眼表现不仅体现了短剧市场的巨大潜力，也证明了剧投投平台在项目筛选和风险控制方面的专业能力。

报告期内，剧投投平台共完成投资项目52个，总投资金额达到3.2亿元，实现总收益5.76亿元。其中，《都市情缘》系列项目表现最为突出，投资回报率高达350%，成为平台历史上最成功的投资案例之一。

## 投资业绩分析

### 整体表现

**投资规模**：
- 总投资项目：52个
- 总投资金额：3.2亿元
- 平均单项投资：615万元
- 投资完成率：98.1%

**收益表现**：
- 总实现收益：5.76亿元
- 平均投资回报率：180%
- 最高单项回报率：350%（《都市情缘》系列）
- 盈利项目占比：94.2%

**风险控制**：
- 亏损项目：3个（占比5.8%）
- 平均亏损幅度：15%
- 风险控制有效率：95%以上

### 分类别表现

**按投资规模分类**：

*大型项目（投资额>1000万）*：
- 项目数量：8个
- 平均回报率：220%
- 代表项目：《都市情缘》、《霸道总裁的小娇妻》

*中型项目（投资额500-1000万）*：
- 项目数量：23个
- 平均回报率：175%
- 代表项目：《重生之商界女王》、《校园恋爱物语》

*小型项目（投资额<500万）*：
- 项目数量：21个
- 平均回报率：145%
- 代表项目：《青春校园》、《都市白领》

**按题材分类**：

*都市情感类*：
- 项目数量：18个
- 平均回报率：195%
- 市场接受度最高，用户付费意愿强

*古装言情类*：
- 项目数量：12个
- 平均回报率：165%
- 制作成本相对较高，但市场潜力巨大

*现代悬疑类*：
- 项目数量：10个
- 平均回报率：170%
- 新兴题材，增长潜力显著

*校园青春类*：
- 项目数量：8个
- 平均回报率：155%
- 目标用户群体明确，变现模式多样

*其他类型*：
- 项目数量：4个
- 平均回报率：140%
- 包括科幻、奇幻等创新题材

## 明星项目案例分析

### 《都市情缘》系列

**项目基本信息**：
- 投资金额：1200万元
- 制作周期：3个月
- 总集数：60集
- 单集时长：2-3分钟

**商业表现**：
- 总收益：5400万元
- 投资回报率：350%
- 播放量：15.8亿次
- 付费用户：280万人

**成功因素分析**：

*内容质量*：该项目在剧本创作、演员选择、后期制作等各个环节都保持了高水准，故事情节紧凑，人物形象鲜明，深受观众喜爱。

*营销策略*：采用了多平台联合推广的策略，通过社交媒体、短视频平台等渠道进行精准营销，有效提升了项目的知名度和影响力。

*商业模式*：创新性地采用了"免费观看+付费解锁"的模式，既保证了用户的观看体验，又实现了良好的商业变现。

*团队实力*：制作团队具有丰富的短剧制作经验，对市场需求有深刻理解，能够准确把握观众喜好。

### 《霸道总裁的小娇妻》

**项目基本信息**：
- 投资金额：800万元
- 制作周期：2.5个月
- 总集数：45集
- 单集时长：2分钟

**商业表现**：
- 总收益：2100万元
- 投资回报率：262%
- 播放量：12.3亿次
- 付费用户：195万人

**成功因素分析**：

*题材选择*：霸道总裁题材一直是短剧市场的热门选择，具有稳定的受众基础和较强的商业价值。

*制作效率*：项目在保证质量的前提下，有效控制了制作周期和成本，提高了投资效率。

*平台优势*：充分利用了剧投投平台的资源优势，在项目推广、用户获取等方面获得了有力支持。

## 市场趋势分析

### 用户行为变化

**付费意愿提升**：
数据显示，用户对优质短剧内容的付费意愿正在快速提升。Q2季度，平台用户的平均付费金额达到35元，相比Q1增长了40%。

**观看习惯优化**：
用户的观看习惯正在向更加理性的方向发展，对内容质量的要求不断提高，这为优质项目创造了更好的市场环境。

**社交传播增强**：
短剧内容的社交传播属性日益凸显，用户通过分享、讨论等方式参与到内容传播中，形成了良性的传播循环。

### 制作水准提升

**技术升级**：
短剧制作的技术水准正在快速提升，从拍摄设备、后期制作到音效配乐，各个环节都在向专业化方向发展。

**人才聚集**：
越来越多的专业影视人才开始关注短剧领域，为行业发展注入了新的活力。

**标准化进程**：
行业正在建立更加标准化的制作流程和质量标准，这有助于提高整体制作效率和质量稳定性。

### 商业模式创新

**多元化变现**：
除了传统的付费观看模式外，IP衍生品开发、线下活动、品牌合作等新的变现方式正在兴起。

**精准营销**：
基于大数据分析的精准营销模式正在成为主流，能够更有效地触达目标用户群体。

**生态化发展**：
短剧产业正在向生态化方向发展，形成了从内容创作、制作发行到商业变现的完整产业链。

## 风险因素分析

### 市场风险

**竞争加剧**：
随着市场的快速发展，竞争也在不断加剧。新入局者的增多可能会对现有项目的市场表现产生影响。

**用户需求变化**：
用户需求的快速变化要求制作方能够及时调整内容策略，否则可能面临市场风险。

**政策变化**：
监管政策的变化可能对行业发展产生重要影响，需要密切关注政策动向。

### 项目风险

**内容风险**：
内容质量的不稳定性是短剧投资面临的主要风险之一，需要建立完善的质量控制体系。

**制作风险**：
制作过程中可能出现的各种问题，如演员档期、技术故障等，都可能影响项目的按时完成。

**市场接受度风险**：
即使是高质量的内容，也可能因为市场接受度不高而影响商业表现。

### 应对策略

**多元化投资**：
通过投资不同类型、不同规模的项目，分散投资风险，提高整体投资组合的稳定性。

**专业化管理**：
建立专业的项目管理团队，从项目筛选、制作监督到市场推广，全程进行专业化管理。

**数据驱动决策**：
充分利用平台的数据优势，通过数据分析指导投资决策，提高投资成功率。

## 下半年展望

### 市场预期

基于Q2的优异表现和市场发展趋势，我们对下半年的市场表现保持乐观态度。预计下半年平台投资规模将达到5亿元，投资项目数量将超过80个。

### 投资策略

**重点布局**：
- 继续加大对都市情感类项目的投资力度
- 积极探索古装言情、现代悬疑等新兴题材
- 关注技术创新对内容制作的推动作用

**风险控制**：
- 进一步完善项目筛选标准
- 加强制作过程的监督管理
- 建立更加完善的风险预警机制

**生态建设**：
- 加强与优质制作团队的合作
- 完善平台服务体系
- 推动行业标准化发展

## 结语

Q2的优异表现证明了短剧投资的巨大潜力，也体现了剧投投平台的专业能力。面向未来，我们将继续坚持专业化、规范化的发展道路，为投资者创造更大的价值，为行业发展贡献更多力量。

我们相信，在各方的共同努力下，短剧行业必将迎来更加辉煌的发展前景，剧投投平台也将在这一进程中发挥更加重要的作用。

*注：本报告数据截至2024年6月30日，仅供参考。投资有风险，决策需谨慎。*
    `
  },
  {
    id: 4,
    title: "《重生之商界女王》项目完成拍摄，预计9月上线各大平台",
    summary: "剧投投平台重点投资项目《重生之商界女王》已完成全部拍摄工作，该项目总投资650万元，预计将于9月中旬在各大短剧平台同步上线。",
    category: "项目更新",
    author: "项目组",
    publishDate: "2024-08-08",
    readCount: 9680,
    coverImage: "https://via.placeholder.com/800x450/F59E0B/FFFFFF?text=项目更新",
    content: `项目详情内容...`
  },
  {
    id: 5,
    title: "短剧出海正当时：中国短剧在东南亚市场表现亮眼",
    summary: "中国短剧正在加速出海步伐，在东南亚市场表现尤为突出。数据显示，中国短剧在泰国、越南、印尼等国的播放量同比增长超过300%。",
    category: "行业动态",
    author: "国际市场研究组",
    publishDate: "2024-08-05",
    readCount: 11200,
    coverImage: "https://via.placeholder.com/800x450/06B6D4/FFFFFF?text=出海报告",
    content: `出海报告详情内容...`
  },
  {
    id: 6,
    title: "AI技术赋能短剧制作：成本降低40%，效率提升300%",
    summary: "人工智能技术正在深刻改变短剧制作流程，从剧本创作到后期制作，AI工具的应用让制作成本降低40%，制作效率提升300%。",
    category: "行业动态",
    author: "技术创新研究院",
    publishDate: "2024-08-02",
    readCount: 13580,
    coverImage: "https://via.placeholder.com/800x450/EF4444/FFFFFF?text=AI技术",
    content: `AI技术应用详情内容...`
  }
])

// 当前新闻
const currentNews = ref<any>(null)
const loading = ref(true)
const error = ref('')

// 获取新闻ID
const newsId = computed(() => {
  return parseInt(route.params.id as string)
})

// 加载新闻详情
const loadNewsDetail = () => {
  loading.value = true
  error.value = ''
  
  try {
    const news = newsData.value.find(item => item.id === newsId.value)
    if (news) {
      currentNews.value = news
      // 增加阅读量
      news.readCount += 1
    } else {
      error.value = '新闻不存在'
    }
  } catch (err) {
    console.error('加载新闻详情失败:', err)
    error.value = '加载新闻详情失败'
  } finally {
    loading.value = false
  }
}

// 返回新闻列表
const goBack = () => {
  router.go(-1)
}

// 分享功能
const shareNews = () => {
  if (navigator.share) {
    navigator.share({
      title: currentNews.value?.title,
      text: currentNews.value?.summary,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    alert('链接已复制到剪贴板')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadNewsDetail()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-lg text-gray-600">加载中...</span>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="text-red-500 text-lg mb-4">{{ error }}</div>
        <button 
          @click="goBack"
          class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
        >
          返回
        </button>
      </div>
    </div>

    <!-- 新闻详情 -->
    <div v-else-if="currentNews" class="max-w-4xl mx-auto">
      <!-- 头部导航 -->
      <div class="bg-white shadow-sm border-b">
        <div class="px-4 py-4">
          <div class="flex items-center justify-between">
            <button 
              @click="goBack"
              class="flex items-center text-gray-600 hover:text-primary transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              返回
            </button>
            
            <button 
              @click="shareNews"
              class="flex items-center text-gray-600 hover:text-primary transition-colors"
            >
              <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              分享
            </button>
          </div>
        </div>
      </div>

      <!-- 文章内容 -->
      <article class="bg-white">
        <!-- 封面图 -->
        <div class="relative h-64 md:h-96 overflow-hidden">
          <img 
            :src="currentNews.coverImage" 
            :alt="currentNews.title"
            class="w-full h-full object-cover"
          />
          <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <!-- 文章头部信息 -->
        <div class="px-6 py-8">
          <!-- 分类标签 -->
          <div class="mb-4">
            <span class="inline-block px-3 py-1 bg-primary text-white text-sm font-medium rounded-full">
              {{ currentNews.category }}
            </span>
          </div>

          <!-- 标题 -->
          <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">
            {{ currentNews.title }}
          </h1>

          <!-- 文章信息 -->
          <div class="flex flex-wrap items-center text-sm text-gray-500 mb-8 space-x-6">
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              {{ currentNews.author }}
            </div>
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              {{ currentNews.publishDate }}
            </div>
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {{ currentNews.readCount.toLocaleString() }} 阅读
            </div>
          </div>

          <!-- 摘要 -->
          <div class="bg-gray-50 p-6 rounded-lg mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">文章摘要</h3>
            <p class="text-gray-700 leading-relaxed">{{ currentNews.summary }}</p>
          </div>

          <!-- 正文内容 -->
          <div class="prose prose-lg max-w-none">
            <div v-html="currentNews.content.replace(/\n/g, '<br>')"></div>
          </div>
        </div>
      </article>

      <!-- 底部操作栏 -->
      <div class="bg-white border-t px-6 py-4">
        <div class="flex items-center justify-between">
          <button 
            @click="goBack"
            class="flex items-center px-4 py-2 text-gray-600 hover:text-primary transition-colors"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            返回列表
          </button>
          
          <button 
            @click="shareNews"
            class="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
          >
            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            分享文章
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  color: #1f2937;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.75rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.5rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1.5rem;
  line-height: 1.7;
  color: #374151;
}

.prose ul, .prose ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.prose strong {
  font-weight: 600;
  color: #1f2937;
}

.prose em {
  font-style: italic;
}
</style>
